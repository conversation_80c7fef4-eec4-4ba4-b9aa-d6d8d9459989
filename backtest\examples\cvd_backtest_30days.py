#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CVD策略30天Backtrader回测

基于CVD.md文档和现有backtrader架构，实现恐慌与吸收策略的30天回测。
严格遵循MyGameNotes.md的系统完整性规则，使用现有的backtrader引擎。
"""

import sys
import os
import pandas as pd
import numpy as np
import backtrader as bt
from datetime import datetime, timedelta
from pathlib import Path
import logging
import time
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

# 导入现有的backtrader系统组件
from backtest.backtrader.core import BacktraderEngine, BacktraderStrategy
from backtest.base import BacktestResults

# 导入CVD相关组件
from freqtrade_bot.user_data.strategies.panic_absorption_strategy import PanicAbsorptionStrategy
from freqtrade_bot.user_data.data_providers.cvd_data_provider import CVDDataProvider
from indicators.cvd_calculator import MultiLayerCVDCalculator, CVDThresholds

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CVDBacktraderStrategy(bt.Strategy):
    """
    CVD策略的Backtrader适配器
    
    将FreqTrade的PanicAbsorptionStrategy适配到Backtrader框架中，
    基于现有架构，避免重复实现。
    """
    
    params = (
        ('freqtrade_strategy', None),  # FreqTrade策略实例
        ('cvd_data_provider', None),   # CVD数据提供者
        ('pair_name', 'BTC/USDT'),     # 交易对名称
        ('debug_mode', False),         # 调试模式
        ('enable_dynamic_thresholds', True),  # 启用动态阈值
    )
    
    def __init__(self):
        """初始化CVD策略适配器"""
        self.freqtrade_strategy = self.params.freqtrade_strategy
        self.cvd_data_provider = self.params.cvd_data_provider
        self.pair_name = self.params.pair_name
        self.debug_mode = self.params.debug_mode
        
        # 交易统计
        self.trade_count = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_pnl = 0.0
        
        # CVD信号数据
        self.indicators_data = None
        self.signals_data = None
        
        # 订单管理
        self.order = None
        self.position_size = 0
        
        logger.info(f"CVD策略适配器初始化完成 - {self.pair_name}")
        
    def start(self):
        """策略开始时调用，准备CVD数据和信号"""
        logger.info("开始准备CVD数据和信号...")
        
        try:
            # 转换Backtrader数据为pandas DataFrame
            dataframe = self._convert_bt_data_to_pandas()
            
            if dataframe.empty:
                logger.error("数据转换失败，无法生成CVD信号")
                return
                
            logger.info(f"数据转换完成，共 {len(dataframe)} 条记录")
            
            # 使用FreqTrade策略生成指标和信号
            metadata = {'pair': self.pair_name}
            
            # 生成技术指标
            self.indicators_data = self.freqtrade_strategy.populate_indicators(
                dataframe.copy(), metadata
            )
            
            # 生成入场信号
            self.signals_data = self.freqtrade_strategy.populate_entry_trend(
                self.indicators_data.copy(), metadata
            )
            
            # 生成出场信号
            self.signals_data = self.freqtrade_strategy.populate_exit_trend(
                self.signals_data, metadata
            )
            
            # 统计信号数量
            long_signals = self.signals_data.get('enter_long', pd.Series()).sum()
            short_signals = self.signals_data.get('enter_short', pd.Series()).sum()
            exit_long_signals = self.signals_data.get('exit_long', pd.Series()).sum()
            exit_short_signals = self.signals_data.get('exit_short', pd.Series()).sum()
            
            logger.info(f"CVD信号生成完成:")
            logger.info(f"  多头入场信号: {long_signals}")
            logger.info(f"  空头入场信号: {short_signals}")
            logger.info(f"  多头出场信号: {exit_long_signals}")
            logger.info(f"  空头出场信号: {exit_short_signals}")
            
            # 检查CVD背离信号
            if 'bullish_divergence' in self.signals_data.columns:
                bullish_div = self.signals_data['bullish_divergence'].sum()
                bearish_div = self.signals_data['bearish_divergence'].sum()
                logger.info(f"  看涨背离信号: {bullish_div}")
                logger.info(f"  看跌背离信号: {bearish_div}")
                
        except Exception as e:
            logger.error(f"CVD信号生成失败: {e}")
            import traceback
            traceback.print_exc()
            
    def _convert_bt_data_to_pandas(self) -> pd.DataFrame:
        """将Backtrader数据转换为pandas DataFrame"""
        try:
            # 获取数据长度
            data_len = len(self.data)
            
            if data_len == 0:
                return pd.DataFrame()
                
            # 提取OHLCV数据
            dates = []
            opens = []
            highs = []
            lows = []
            closes = []
            volumes = []
            
            for i in range(data_len):
                dates.append(bt.num2date(self.data.datetime[i]))
                opens.append(self.data.open[i])
                highs.append(self.data.high[i])
                lows.append(self.data.low[i])
                closes.append(self.data.close[i])
                volumes.append(self.data.volume[i])
                
            # 创建DataFrame
            dataframe = pd.DataFrame({
                'date': dates,
                'open': opens,
                'high': highs,
                'low': lows,
                'close': closes,
                'volume': volumes
            })
            
            # 设置日期索引
            dataframe.set_index('date', inplace=True)
            
            return dataframe
            
        except Exception as e:
            logger.error(f"数据转换失败: {e}")
            return pd.DataFrame()
            
    def next(self):
        """策略主逻辑，每个K线调用一次"""
        # 获取当前位置索引
        pos_idx = len(self) - 1
        
        # 检查信号数据是否可用
        if self.signals_data is None or pos_idx >= len(self.signals_data):
            return
            
        # 获取当前信号
        current_signals = self.signals_data.iloc[pos_idx]
        
        # 检查是否有挂单
        if self.order:
            return  # 等待订单完成
            
        # 获取当前价格
        current_price = self.data.close[0]
        
        # 处理入场信号
        if not self.position:  # 无持仓时
            # 多头入场信号
            if current_signals.get('enter_long', 0) == 1:
                self.order = self.buy(size=1)
                self.log(f'多头入场信号 @ {current_price:.2f}')
                
            # 空头入场信号
            elif current_signals.get('enter_short', 0) == 1:
                self.order = self.sell(size=1)
                self.log(f'空头入场信号 @ {current_price:.2f}')
                
        else:  # 有持仓时
            # 多头出场信号
            if self.position.size > 0 and current_signals.get('exit_long', 0) == 1:
                self.order = self.close()
                self.log(f'多头出场信号 @ {current_price:.2f}')
                
            # 空头出场信号
            elif self.position.size < 0 and current_signals.get('exit_short', 0) == 1:
                self.order = self.close()
                self.log(f'空头出场信号 @ {current_price:.2f}')
                
    def notify_order(self, order):
        """订单状态通知"""
        if order.status in [order.Submitted, order.Accepted]:
            return
            
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(f'买入执行: 价格={order.executed.price:.2f}, '
                        f'数量={order.executed.size:.2f}, '
                        f'手续费={order.executed.comm:.2f}')
            else:
                self.log(f'卖出执行: 价格={order.executed.price:.2f}, '
                        f'数量={order.executed.size:.2f}, '
                        f'手续费={order.executed.comm:.2f}')
                        
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log(f'订单失败: {order.status}')
            
        self.order = None
        
    def notify_trade(self, trade):
        """交易完成通知"""
        if not trade.isclosed:
            return
            
        self.trade_count += 1
        pnl = trade.pnl
        self.total_pnl += pnl
        
        if pnl > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
            
        self.log(f'交易完成: 盈亏={pnl:.2f}, 总盈亏={self.total_pnl:.2f}, '
                f'胜率={self.winning_trades}/{self.trade_count}')
                
    def log(self, txt, dt=None):
        """日志输出"""
        dt = dt or self.datas[0].datetime.date(0)
        if self.debug_mode:
            print(f'{dt.isoformat()}: {txt}')
        logger.info(f'{dt.isoformat()}: {txt}')
        
    def stop(self):
        """策略结束时调用"""
        win_rate = (self.winning_trades / self.trade_count * 100) if self.trade_count > 0 else 0
        
        logger.info(f"CVD策略回测完成:")
        logger.info(f"  总交易次数: {self.trade_count}")
        logger.info(f"  盈利交易: {self.winning_trades}")
        logger.info(f"  亏损交易: {self.losing_trades}")
        logger.info(f"  胜率: {win_rate:.2f}%")
        logger.info(f"  总盈亏: {self.total_pnl:.2f}")


class CVDBacktestRunner:
    """
    CVD策略30天回测运行器
    
    基于现有backtrader架构，实现CVD策略的完整回测流程。
    """
    
    def __init__(self, 
                 initial_cash: float = 100000.0,
                 commission: float = 0.001,
                 enable_dynamic_thresholds: bool = True):
        """
        初始化回测运行器
        
        Args:
            initial_cash: 初始资金
            commission: 手续费率
            enable_dynamic_thresholds: 启用动态CVD阈值
        """
        self.initial_cash = initial_cash
        self.commission = commission
        self.enable_dynamic_thresholds = enable_dynamic_thresholds
        
        # 初始化组件
        self.freqtrade_strategy = None
        self.cvd_data_provider = None
        self.cerebro = None
        
        logger.info(f"CVD回测运行器初始化完成")
        logger.info(f"  初始资金: ${initial_cash:,.2f}")
        logger.info(f"  手续费率: {commission*100:.3f}%")
        logger.info(f"  动态阈值: {'启用' if enable_dynamic_thresholds else '禁用'}")
        
    def prepare_strategy(self) -> bool:
        """准备FreqTrade策略和CVD数据提供者"""
        try:
            # 初始化FreqTrade策略
            config = {
                'datadir': 'user_data/data',
                'timeframe': '5m'
            }
            
            self.freqtrade_strategy = PanicAbsorptionStrategy(config)
            
            # 初始化CVD数据提供者
            self.cvd_data_provider = CVDDataProvider(
                config,
                enable_dynamic_thresholds=self.enable_dynamic_thresholds
            )
            
            logger.info("策略和数据提供者准备完成")
            return True
            
        except Exception as e:
            logger.error(f"策略准备失败: {e}")
            return False

    def load_data(self, symbol: str = 'BTCUSDT', days: int = 30) -> Optional[pd.DataFrame]:
        """
        加载最近30天的数据

        Args:
            symbol: 交易对符号
            days: 数据天数

        Returns:
            OHLCV数据DataFrame
        """
        try:
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            logger.info(f"加载 {symbol} 数据: {start_date.date()} 到 {end_date.date()}")

            # 尝试从FreqTrade数据目录加载
            data_dir = Path('freqtrade-bot/user_data/data/binance')

            # 查找对应的数据文件
            pair_name = symbol.replace('USDT', '_USDT')
            data_files = list(data_dir.glob(f"{pair_name}-5m-*.json"))

            if not data_files:
                logger.warning(f"未找到 {symbol} 的历史数据文件")
                # 生成模拟数据用于测试
                return self._generate_sample_data(start_date, end_date)

            # 加载最新的数据文件
            latest_file = sorted(data_files)[-1]
            logger.info(f"加载数据文件: {latest_file}")

            data = pd.read_json(latest_file)

            # 转换时间戳
            data['date'] = pd.to_datetime(data[0], unit='ms')
            data['open'] = data[1].astype(float)
            data['high'] = data[2].astype(float)
            data['low'] = data[3].astype(float)
            data['close'] = data[4].astype(float)
            data['volume'] = data[5].astype(float)

            # 选择需要的列并设置索引
            data = data[['date', 'open', 'high', 'low', 'close', 'volume']]
            data.set_index('date', inplace=True)

            # 过滤日期范围
            data = data[(data.index >= start_date) & (data.index <= end_date)]

            logger.info(f"数据加载完成: {len(data)} 条记录")
            return data

        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            # 生成模拟数据作为备选
            return self._generate_sample_data(start_date, end_date)

    def _generate_sample_data(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """生成模拟数据用于测试"""
        logger.info("生成模拟数据用于测试...")

        # 生成5分钟间隔的时间序列
        date_range = pd.date_range(start=start_date, end=end_date, freq='5T')

        # 生成模拟价格数据
        np.random.seed(42)  # 确保可重复性

        base_price = 50000  # BTC基础价格
        price_changes = np.random.normal(0, 0.002, len(date_range))  # 0.2%标准差
        prices = [base_price]

        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)

        prices = np.array(prices)

        # 生成OHLCV数据
        data = pd.DataFrame({
            'open': prices,
            'high': prices * (1 + np.random.uniform(0, 0.01, len(prices))),
            'low': prices * (1 - np.random.uniform(0, 0.01, len(prices))),
            'close': prices * (1 + np.random.normal(0, 0.001, len(prices))),
            'volume': np.random.uniform(100, 1000, len(prices))
        }, index=date_range)

        # 确保high >= max(open, close) 和 low <= min(open, close)
        data['high'] = np.maximum(data['high'], np.maximum(data['open'], data['close']))
        data['low'] = np.minimum(data['low'], np.minimum(data['open'], data['close']))

        logger.info(f"模拟数据生成完成: {len(data)} 条记录")
        return data

    def run_backtest(self, symbol: str = 'BTCUSDT', days: int = 30, debug: bool = False) -> Dict[str, Any]:
        """
        运行CVD策略30天回测

        Args:
            symbol: 交易对符号
            days: 回测天数
            debug: 调试模式

        Returns:
            回测结果字典
        """
        logger.info(f"开始CVD策略30天回测: {symbol}")

        # 1. 准备策略
        if not self.prepare_strategy():
            return {'error': '策略准备失败'}

        # 2. 加载数据
        data = self.load_data(symbol, days)
        if data is None or data.empty:
            return {'error': '数据加载失败'}

        # 3. 创建Backtrader引擎
        self.cerebro = bt.Cerebro()

        # 设置初始资金和手续费
        self.cerebro.broker.setcash(self.initial_cash)
        self.cerebro.broker.setcommission(commission=self.commission)

        # 设置仓位管理器
        self.cerebro.addsizer(bt.sizers.PercentSizer, percents=10)  # 每次使用10%资金

        # 转换数据格式
        bt_data = bt.feeds.PandasData(
            dataname=data,
            datetime=None,  # 使用索引作为时间
            open=0,
            high=1,
            low=2,
            close=3,
            volume=4,
            openinterest=-1
        )

        # 添加数据到引擎
        self.cerebro.adddata(bt_data)

        # 添加策略
        self.cerebro.addstrategy(
            CVDBacktraderStrategy,
            freqtrade_strategy=self.freqtrade_strategy,
            cvd_data_provider=self.cvd_data_provider,
            pair_name=symbol.replace('USDT', '/USDT'),
            debug_mode=debug,
            enable_dynamic_thresholds=self.enable_dynamic_thresholds
        )

        # 添加分析器
        self.cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        self.cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        self.cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        self.cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')

        # 记录开始时间和资金
        start_time = time.time()
        start_cash = self.cerebro.broker.getvalue()

        logger.info(f"开始回测...")
        logger.info(f"  数据范围: {data.index[0]} 到 {data.index[-1]}")
        logger.info(f"  数据点数: {len(data)}")
        logger.info(f"  初始资金: ${start_cash:,.2f}")

        # 运行回测
        results = self.cerebro.run()

        # 计算回测时间
        backtest_time = time.time() - start_time

        # 获取最终资金
        end_cash = self.cerebro.broker.getvalue()

        # 提取分析结果
        strategy_result = results[0]

        # 构建结果字典
        result_dict = {
            'symbol': symbol,
            'days': days,
            'data_points': len(data),
            'backtest_time': backtest_time,
            'start_cash': start_cash,
            'end_cash': end_cash,
            'total_return': (end_cash - start_cash) / start_cash * 100,
            'trade_count': strategy_result.trade_count,
            'winning_trades': strategy_result.winning_trades,
            'losing_trades': strategy_result.losing_trades,
            'win_rate': (strategy_result.winning_trades / strategy_result.trade_count * 100) if strategy_result.trade_count > 0 else 0,
            'total_pnl': strategy_result.total_pnl,
            'dynamic_thresholds': self.enable_dynamic_thresholds
        }

        # 添加分析器结果
        try:
            sharpe_ratio = strategy_result.analyzers.sharpe.get_analysis().get('sharperatio', 0)
            drawdown = strategy_result.analyzers.drawdown.get_analysis()
            trades_analysis = strategy_result.analyzers.trades.get_analysis()

            result_dict.update({
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': drawdown.get('max', {}).get('drawdown', 0),
                'max_drawdown_period': drawdown.get('max', {}).get('len', 0),
                'trades_analysis': trades_analysis
            })
        except Exception as e:
            logger.warning(f"分析器结果提取失败: {e}")

        logger.info(f"回测完成，耗时 {backtest_time:.2f} 秒")

        return result_dict

    def plot_results(self, title: str = "CVD策略30天回测结果"):
        """绘制回测结果图表"""
        if self.cerebro is None:
            logger.error("请先运行回测")
            return

        try:
            self.cerebro.plot(style='candlestick', barup='green', bardown='red')
            logger.info("回测图表已生成")
        except Exception as e:
            logger.error(f"图表生成失败: {e}")


def main():
    """主函数 - CVD策略30天回测"""
    print("=" * 60)
    print("CVD策略30天Backtrader回测")
    print("基于恐慌与吸收策略 - 多层CVD背离检测")
    print("=" * 60)

    # 创建回测运行器
    runner = CVDBacktestRunner(
        initial_cash=100000.0,
        commission=0.001,
        enable_dynamic_thresholds=True
    )

    # 运行回测
    results = runner.run_backtest(
        symbol='BTCUSDT',
        days=30,
        debug=False
    )

    # 显示结果
    if 'error' in results:
        print(f"❌ 回测失败: {results['error']}")
        return

    print(f"\n📊 CVD策略回测结果:")
    print(f"  交易对: {results['symbol']}")
    print(f"  回测天数: {results['days']} 天")
    print(f"  数据点数: {results['data_points']:,}")
    print(f"  回测耗时: {results['backtest_time']:.2f} 秒")
    print(f"  动态阈值: {'启用' if results['dynamic_thresholds'] else '禁用'}")

    print(f"\n💰 资金表现:")
    print(f"  初始资金: ${results['start_cash']:,.2f}")
    print(f"  最终资金: ${results['end_cash']:,.2f}")
    print(f"  总收益率: {results['total_return']:+.2f}%")
    print(f"  总盈亏: ${results['total_pnl']:+,.2f}")

    print(f"\n📈 交易统计:")
    print(f"  总交易次数: {results['trade_count']}")
    print(f"  盈利交易: {results['winning_trades']}")
    print(f"  亏损交易: {results['losing_trades']}")
    print(f"  胜率: {results['win_rate']:.2f}%")

    if 'sharpe_ratio' in results:
        print(f"\n📊 风险指标:")
        print(f"  夏普比率: {results['sharpe_ratio']:.3f}")
        print(f"  最大回撤: {results['max_drawdown']:.2f}%")
        print(f"  最大回撤期: {results['max_drawdown_period']} 个周期")

    # 绘制结果图表
    try:
        runner.plot_results("CVD策略30天回测结果")
    except Exception as e:
        print(f"⚠️ 图表显示跳过: {e}")

    print(f"\n✅ CVD策略30天回测完成!")


if __name__ == '__main__':
    main()
