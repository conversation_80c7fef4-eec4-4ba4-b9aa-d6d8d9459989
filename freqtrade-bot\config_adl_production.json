{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 3, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.95, "available_capital": 70.0, "dry_run": true, "dry_run_wallet": 100000, "cancel_open_orders_on_exit": true, "trading_mode": "futures", "margin_mode": "isolated", "max_leverage": 4.0, "adl_strategy_params": {"atr_period": 14, "atr_sma_period": 50, "atr_multiplier": 1.8, "zscore_period": 50, "zscore_threshold": 1.85, "atr_stop_multiplier": 0.42, "atr_profit_multiplier": 0.55, "volume_confirmation": {"volume_sma_period": 20, "volume_multiplier": 1.0, "enable_volume_filter": true}, "basic_thresholds": {"strong_signal_threshold": 0.4, "high_quality_signal_threshold": 1.0, "signal_strength_decline_threshold": 0.5, "default_fillna_value": 1.0}, "atr_volatility_levels": {"very_high_threshold": 1.8, "high_threshold": 1.3, "medium_threshold": 1.0, "low_threshold": 0.7, "default_ratio": 1.0}, "profit_loss_thresholds": {"quick_loss_threshold": -0.04, "profit_protection_threshold": 0.002, "min_profit_for_maturity_exit": 0.001, "min_profit_for_signal_tracking": 0.001, "signal_maturity_time_minutes": 8, "max_loss_for_position_adjustment": -0.001}, "volatility_adaptive_multipliers": {"high_volatility_threshold": 1.3, "high_volatility_multiplier": 1.4, "medium_volatility_threshold": 1.1, "medium_volatility_multiplier": 1.2, "low_volatility_multiplier": 0.9, "position_high_volatility_threshold": 1.5, "position_high_volatility_multiplier": 1.3, "position_medium_volatility_threshold": 1.2, "position_medium_volatility_multiplier": 1.1, "position_low_volatility_multiplier": 0.9}, "position_adjustment": {"tier1_min_signal_strength": 1.3, "tier1_position_multiplier": 0.8, "tier1_max_additions": 2, "tier2_min_signal_strength": 0.8, "tier2_position_multiplier": 0.6, "tier2_max_additions": 1, "tier3_min_signal_strength": 0.5, "tier3_position_multiplier": 0.4, "tier3_max_additions": 1, "default_min_signal_strength": 0.6, "default_position_multiplier": 0.9, "default_max_additions": 2, "min_interval_minutes": 10}, "leverage_settings": {"fallback_leverage_step3_disabled": 2.5, "fallback_leverage_no_tag": 1.6}, "price_adjustment": {"atr_adjustment_ratio": 0.05, "max_adjustment_long": 0.995, "max_adjustment_short": 1.005}, "step3_optimization": {"enable": true, "signal_tier_system": {"tier1_strength_min": 1.2, "tier2_strength_min": 0.8, "tier3_strength_min": 0.5, "tier1_max_trades": 3, "tier2_max_trades": 3, "tier3_max_trades": 4}, "aggressive_time_management": {"enable": true, "tier1_time_limits": [30, 35, 40, 45, 50], "tier2_time_limits": [25, 30, 35, 40, 45], "tier3_time_limits": [22, 27, 32, 37, 42], "profit_protection_time": 8, "profit_protection_threshold": 1.0, "profit_protection_time_limit": 20, "quick_loss_threshold": -1.5, "quick_loss_time_limit": 10}, "momentum_confirmation": {"enable": true, "price_acceleration_periods": 3, "min_acceleration_ratio": 0.9, "volume_burst_multiplier": 0.9, "rsi_oversold_threshold": 50, "rsi_overbought_threshold": 50}, "profit_protection": {"enable": true, "min_profit_for_protection": 0.003, "protected_time_limit": 8, "quick_exit_loss_threshold": -0.008}, "signal_quality_control": {"consecutive_loss_limit": 3, "daily_signal_limit": 15, "cooldown_after_loss": 2}, "signal_quality_filter": {"min_signal_strength": 0.6, "volume_confirmation": true, "avoid_consecutive_signals": true, "signal_cooldown_minutes": 10}, "volume_confirmation": {"volume_sma_period": 20, "volume_multiplier": 1.0, "enable_volume_filter": true}}, "minimal_roi": {"0": 0.038, "2": 0.018, "4": 0.009, "6": 0}, "maker_only": true, "use_custom_stoploss": false, "leverage_revolution": {"enable": true, "base_leverage": 3.0, "tier1_leverage": 2.8, "tier2_leverage": 3.8, "tier3_leverage": 3.5, "volatility_adjustment": true, "min_leverage": 2, "risk_scaling": true}, "multi_position_optimization": {"enable": true, "signal_strength_threshold": 1.3, "time_intervals": {"tier1_min_minutes": 6, "tier2_min_minutes": 10, "tier3_min_minutes": 12}, "position_limits": {"max_same_direction": 2, "max_same_tier": 2, "max_total_positions": 3}, "quality_requirements": {"tier1_only_when_crowded": true, "signal_upgrade_ratio": 1.3}}, "signal_recovery_system": {"enable": true, "priority_signal_strength": 1.2, "dynamic_time_adjustment": {"enable": true, "high_strength_reduction": 0.3, "signal_density_threshold": 2, "low_density_relaxation": 1.5}, "position_replacement": {"enable": true, "min_quality_gap": 0.5, "replacement_time_limit": 10}, "rejected_signal_tracking": {"enable": true, "track_reasons": true, "quality_analysis": true}}, "enhanced_risk_management": {"enable": true, "leverage_based_stoploss": true, "quick_exit_multiplier": 0.9, "dynamic_position_sizing": true, "volatility_protection": true}, "multi_exit_system": {"enable": true, "partial_exit_levels": [0.25, 0.4, 0.6, 0.8], "tier1_profit_targets": [0.008, 0.016, 0.028, 0.045], "tier2_profit_targets": [0.004, 0.008, 0.014, 0.024], "tier3_profit_targets": [0.007, 0.012, 0.018, 0.03], "volatility_adaptive": true, "signal_maturity_tracking": true, "early_profit_protection": true, "aggressive_partial_exit": true}, "enhanced_signal_quality": {"enable": true, "negative_signal_filter": true, "signal_momentum_tracking": true, "early_warning_system": true, "adaptive_profit_targets": true}, "time_management": {"tier1_base_interval_minutes": 8, "tier2_base_interval_minutes": 12, "tier3_base_interval_minutes": 15, "high_strength_reduction": 0.3, "signal_density_threshold": 2, "low_density_relaxation": 1.5}}, "liquidation_buffer": 0.05, "timeframe": "5m", "strategy": "ADLAnticipationStrategy", "strategy_path": "user_data/strategies/", "unfilledtimeout": {"entry": 60, "exit": 60, "exit_timeout_count": 0, "unit": "seconds"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "eTqhJbBEFcQ3Wh1mrgoc2rAwVxTZn7KHtdC2WZ0zuTgDo9VTZd7RmgQdAaOaBLXA", "secret": "sdENnLxeQ3Jn4INGMSu92yRot3aI5FbjnqPmbfRURKe82n1z2f20S5zrP5EmJ2Wd", "password": "", "sandbox": false, "ccxt_config": {"enableRateLimit": true, "rateLimit": 200, "timeout": 30000, "options": {"defaultType": "future", "recvWindow": 10000}, "proxies": {"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"}}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 200, "timeout": 30000, "proxies": {"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"}}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT", "BNB/USDT:USDT", "XRP/USDT:USDT", "ADA/USDT:USDT", "AVAX/USDT:USDT", "LINK/USDT:USDT", "NEAR/USDT:USDT", "WLD/USDT:USDT", "ARB/USDT:USDT", "SUI/USDT:USDT", "ENA/USDT:USDT", "WIF/USDT:USDT", "TRUMP/USDT:USDT", "DOGE/USDT:USDT", "DOT/USDT:USDT", "UNI/USDT:USDT", "LTC/USDT:USDT", "BCH/USDT:USDT", "ATOM/USDT:USDT", "FIL/USDT:USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "api_server": {"enabled": true, "listen_ip_address": "127.0.0.1", "listen_port": 8081, "verbosity": "info", "enable_openapi": false, "jwt_secret_key": "69b5b88ea1cea9f64ca02dbcf16df8fccdaee9a9c2626874ce6471dcbf100611", "ws_token": "RTyNPDzSiJLQ-XmWHbYK1l1veeNPL7Dg4A_30E4dKts", "CORS_origins": ["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000", "http://127.0.0.1:8080"], "username": "ADL_Trader_b85eb671", "password": "Y08D57DnfKHinZaM"}, "bot_name": "ADL-Production-Live", "db_url": "sqlite:///tradesv3.production.sqlite", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 3}, "order_types": {"entry": "limit", "exit": "limit", "emergency_exit": "limit", "stoploss": "limit", "stoploss_on_exchange": false, "stoploss_on_exchange_interval": 60}, "order_time_in_force": {"entry": "GTC", "exit": "GTC"}, "dataformat_ohlcv": "json", "dataformat_trades": "json", "position_adjustment_enable": true, "max_entry_position_adjustment": 4}