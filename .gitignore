# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a PyInstaller script; adding them
#  to your project gitignore file is not necessary.
# *.spec


# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# PEP 582; __pypackages__ directory
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath files
.sage.py

# Environments
#.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# PyDev project settings
.pydevproject

# PyCharm
.idea/
*.iws
*.iml

# VS Code
.vscode/

# Editor-specific temporary files
*~
*.swp
*.swo

# OS-generated files
.DS_Store
Thumbs.db

# MyPy cache
.mypy_cache/

# Personal notes or sensitive information
*.key
credentials.json
secrets.yaml
secrets.yml

# Output folders from examples or tests if they are not meant to be committed
backtest/examples/output/
risk/examples/output/
storage/executions/
storage/logs/
storage/tasks/
# If you have specific large data files that are downloaded or generated
# and should not be in git, add them here, e.g.:
# data/raw_data/*.csv
# data/processed_data/*.hdf5
/storage/monitoring_data
/storage/market_data
*.pyc
/storage
/user_data/data
/user_data
/user_data
/freqtrade-bot/user_data/backtest_results
/freqtrade-bot/user_data/data/binance
/freqtrade-bot/user_data/backtest_results
