#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易策略模块

包含各种交易策略的实现，基于Strategy基类。
重构版本：使用FreqTrade原生组件，符合系统完整性规则。
"""

# 导入现有有效的策略
try:
    from .smc_strategy import CryptoMomentumStrategy as SMCStrategy
except ImportError:
    SMCStrategy = None

try:
    from .crypto_momentum_strategy import CryptoMomentumStrategy
except ImportError:
    CryptoMomentumStrategy = None

# 导出策略类
__all__ = []
if SMCStrategy:
    __all__.append('SMCStrategy')
if CryptoMomentumStrategy:
    __all__.append('CryptoMomentumStrategy')