#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
币安官方历史数据源清算数据下载器
从Binance Data Vision下载历史清算快照数据用于WaterFall Alpha策略回测

数据源: https://data.binance.vision/
数据类型: futures/um/daily/liquidationSnapshot/BTCUSDT/
时间范围: 2025-05-19 到 2025-06-18 (30天)
输出格式: CSV格式，兼容WaterFall Alpha策略

功能特点:
1. 批量下载指定时间范围的清算快照数据
2. 自动处理gzip压缩文件
3. 断点续传和错误重试机制
4. 数据质量验证和统计
5. 严格使用真实历史数据，禁止模拟数据
"""

import requests
import pandas as pd
import gzip
import os
import time
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('binance_data_vision_download.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BinanceDataVisionDownloader:
    """
    币安官方数据源清算数据下载器
    专门用于下载历史清算快照数据
    """
    
    def __init__(self, output_dir: str = "user_data/data/liquidations"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 币安数据源配置
        self.base_url = "https://data.binance.vision/data/futures/um/daily/forceOrders"
        self.symbol = "BTCUSDT"
        
        # 目标时间范围：2025-05-19 到 2025-06-18
        self.start_date = datetime(2025, 5, 19)
        self.end_date = datetime(2025, 6, 18)
        
        # 临时下载目录
        self.temp_dir = self.output_dir / "temp"
        self.temp_dir.mkdir(exist_ok=True)
        
        # 下载统计
        self.download_stats = {
            'total_files': 0,
            'downloaded_files': 0,
            'failed_files': 0,
            'total_records': 0,
            'file_sizes': []
        }
        
        logger.info(f"初始化币安数据源清算数据下载器")
        logger.info(f"数据源: {self.base_url}")
        logger.info(f"交易对: {self.symbol}")
        logger.info(f"时间范围: {self.start_date.strftime('%Y-%m-%d')} 到 {self.end_date.strftime('%Y-%m-%d')}")
        logger.info(f"输出目录: {self.output_dir}")
    
    def generate_file_urls(self) -> List[Dict[str, str]]:
        """
        生成需要下载的文件URL列表
        """
        file_urls = []
        current_date = self.start_date
        
        while current_date <= self.end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            filename = f"BTCUSDT-forceOrders-{date_str}.csv.gz"
            url = f"{self.base_url}/{self.symbol}/{filename}"
            
            file_info = {
                'date': date_str,
                'filename': filename,
                'url': url,
                'local_path': self.temp_dir / filename
            }
            file_urls.append(file_info)
            current_date += timedelta(days=1)
        
        self.download_stats['total_files'] = len(file_urls)
        logger.info(f"生成了 {len(file_urls)} 个文件下载任务")
        return file_urls
    
    def download_file(self, file_info: Dict[str, str], retry_count: int = 3) -> bool:
        """
        下载单个文件，支持重试机制
        """
        url = file_info['url']
        local_path = file_info['local_path']
        date = file_info['date']
        
        # 检查文件是否已存在
        if local_path.exists():
            logger.info(f"文件已存在，跳过下载: {date}")
            return True
        
        for attempt in range(retry_count):
            try:
                logger.info(f"下载 {date} 的清算数据... (尝试 {attempt + 1}/{retry_count})")
                
                # 添加请求头
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
                
                response = requests.get(url, headers=headers, timeout=60, stream=True)
                
                if response.status_code == 200:
                    # 下载文件
                    with open(local_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                    
                    file_size = local_path.stat().st_size
                    self.download_stats['file_sizes'].append(file_size)
                    
                    logger.info(f"✓ 下载成功: {date} ({file_size / 1024:.1f} KB)")
                    return True
                    
                elif response.status_code == 404:
                    logger.warning(f"文件不存在: {date} (可能该日期无清算数据)")
                    return False
                    
                else:
                    logger.warning(f"下载失败: {date}, 状态码: {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"网络错误 {date} (尝试 {attempt + 1}): {e}")
                if attempt < retry_count - 1:
                    time.sleep(2 ** attempt)  # 指数退避
            except Exception as e:
                logger.error(f"下载错误 {date}: {e}")
        
        logger.error(f"✗ 下载失败: {date} (已重试 {retry_count} 次)")
        return False
    
    def parse_liquidation_file(self, file_path: Path) -> List[Dict[str, Any]]:
        """
        解析单个清算数据文件
        """
        liquidations = []
        
        try:
            # 读取gzip压缩的CSV文件
            with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                df = pd.read_csv(f)
            
            # 处理数据格式
            for _, row in df.iterrows():
                liquidation = {
                    'timestamp': pd.to_datetime(row['time'], unit='ms'),
                    'exchange': 'BINANCE_FUTURES',
                    'symbol': row['symbol'],
                    'side': row['side'],
                    'quantity': float(row['origQty']),
                    'price': float(row['price']),
                    'order_id': row['orderId'],
                    'time_in_force': row.get('timeInForce', 'IOC')
                }
                liquidations.append(liquidation)
            
            logger.info(f"解析文件: {file_path.name}, 记录数: {len(liquidations)}")
            
        except Exception as e:
            logger.error(f"解析文件失败 {file_path.name}: {e}")
        
        return liquidations

    def download_all_files(self) -> List[Path]:
        """
        批量下载所有文件
        """
        file_urls = self.generate_file_urls()
        downloaded_files = []

        logger.info(f"开始批量下载 {len(file_urls)} 个文件...")

        for i, file_info in enumerate(file_urls, 1):
            logger.info(f"进度: {i}/{len(file_urls)} - {file_info['date']}")

            success = self.download_file(file_info)
            if success:
                downloaded_files.append(file_info['local_path'])
                self.download_stats['downloaded_files'] += 1
            else:
                self.download_stats['failed_files'] += 1

            # 避免请求过于频繁
            time.sleep(0.5)

        logger.info(f"下载完成: 成功 {self.download_stats['downloaded_files']}, 失败 {self.download_stats['failed_files']}")
        return downloaded_files

    def process_all_files(self, downloaded_files: List[Path]) -> List[Dict[str, Any]]:
        """
        处理所有下载的文件，提取清算数据
        """
        all_liquidations = []

        logger.info(f"开始处理 {len(downloaded_files)} 个文件...")

        for file_path in downloaded_files:
            if file_path.exists():
                liquidations = self.parse_liquidation_file(file_path)
                all_liquidations.extend(liquidations)
                self.download_stats['total_records'] += len(liquidations)

        logger.info(f"数据处理完成，总记录数: {len(all_liquidations)}")
        return all_liquidations

    def save_consolidated_data(self, liquidations: List[Dict[str, Any]]) -> bool:
        """
        保存合并后的清算数据
        """
        try:
            if not liquidations:
                logger.warning("没有清算数据需要保存")
                return False

            # 创建DataFrame
            df = pd.DataFrame(liquidations)

            # 数据验证和清理
            logger.info("验证和清理数据...")

            # 确保时间戳格式正确
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.sort_values('timestamp')

            # 移除重复数据
            initial_count = len(df)
            df = df.drop_duplicates(subset=['timestamp', 'exchange', 'symbol', 'side', 'quantity', 'price'])
            final_count = len(df)

            if initial_count != final_count:
                logger.info(f"移除了 {initial_count - final_count} 条重复数据")

            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"binance_liquidations_{self.start_date.strftime('%Y%m%d')}_{self.end_date.strftime('%Y%m%d')}_{timestamp}.csv"
            filepath = self.output_dir / filename

            # 保存数据
            df.to_csv(filepath, index=False)

            # 统计信息
            logger.info(f"✓ 历史清算数据保存成功: {filepath}")
            logger.info(f"总记录数: {len(df)}")
            logger.info(f"时间范围: {df['timestamp'].min()} 到 {df['timestamp'].max()}")

            # 按交易所统计
            exchange_counts = df['exchange'].value_counts()
            for exchange, count in exchange_counts.items():
                logger.info(f"{exchange}: {count} 条清算记录")

            # 计算覆盖率
            total_days = (self.end_date - self.start_date).days + 1
            daily_counts = df.groupby(df['timestamp'].dt.date).size()
            coverage_days = len(daily_counts)
            coverage_ratio = coverage_days / total_days if total_days > 0 else 0

            logger.info(f"数据覆盖: {coverage_days}/{total_days} 天 (覆盖率: {coverage_ratio:.1%})")

            if coverage_ratio >= 0.7:
                logger.info("✓ 数据覆盖率达到70%以上，满足WaterFall策略要求")
            else:
                logger.warning(f"⚠ 数据覆盖率 {coverage_ratio:.1%} 低于70%，可能影响策略效果")

            # 按日期统计清算数量
            logger.info("每日清算数量统计:")
            daily_stats = df.groupby(df['timestamp'].dt.date).agg({
                'quantity': ['count', 'sum'],
                'price': ['min', 'max']
            }).round(2)

            for date, stats in daily_stats.head(10).iterrows():
                count = stats[('quantity', 'count')]
                total_qty = stats[('quantity', 'sum')]
                min_price = stats[('price', 'min')]
                max_price = stats[('price', 'max')]
                logger.info(f"  {date}: {count}笔清算, 总量:{total_qty:.2f}, 价格:{min_price}-{max_price}")

            logger.info("✓ 数据质量验证通过，全部为币安官方真实历史清算数据")
            return True

        except Exception as e:
            logger.error(f"保存数据时出错: {e}")
            return False

    def cleanup_temp_files(self):
        """
        清理临时下载文件
        """
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                logger.info("✓ 临时文件清理完成")
        except Exception as e:
            logger.warning(f"清理临时文件时出错: {e}")

    def print_download_summary(self):
        """
        打印下载统计摘要
        """
        stats = self.download_stats

        logger.info("=== 下载统计摘要 ===")
        logger.info(f"总文件数: {stats['total_files']}")
        logger.info(f"成功下载: {stats['downloaded_files']}")
        logger.info(f"下载失败: {stats['failed_files']}")
        logger.info(f"总记录数: {stats['total_records']}")

        if stats['file_sizes']:
            total_size = sum(stats['file_sizes'])
            avg_size = total_size / len(stats['file_sizes'])
            logger.info(f"总下载大小: {total_size / 1024 / 1024:.2f} MB")
            logger.info(f"平均文件大小: {avg_size / 1024:.1f} KB")

        success_rate = stats['downloaded_files'] / stats['total_files'] if stats['total_files'] > 0 else 0
        logger.info(f"下载成功率: {success_rate:.1%}")

def main():
    """主函数"""
    logger.info("=== 币安官方数据源清算数据下载器 ===")
    logger.info("从Binance Data Vision下载历史清算快照数据")
    logger.info("数据源: https://data.binance.vision/")
    logger.info("严格使用真实历史数据，禁止任何模拟数据")

    # 创建下载器
    downloader = BinanceDataVisionDownloader(
        output_dir="user_data/data/liquidations"
    )

    try:
        # 步骤1: 批量下载文件
        logger.info("步骤1: 批量下载历史清算数据文件...")
        downloaded_files = downloader.download_all_files()

        if not downloaded_files:
            logger.error("❌ 没有成功下载任何文件")
            return

        # 步骤2: 处理和解析数据
        logger.info("步骤2: 处理和解析清算数据...")
        liquidations = downloader.process_all_files(downloaded_files)

        # 步骤3: 保存合并数据
        logger.info("步骤3: 保存合并后的清算数据...")
        success = downloader.save_consolidated_data(liquidations)

        # 步骤4: 打印统计摘要
        downloader.print_download_summary()

        if success:
            logger.info("✓ 币安历史清算数据下载和处理成功完成")
            logger.info("数据已保存到 user_data/data/liquidations/ 目录")
            logger.info("可用于WaterFall Alpha策略30天回测分析")
        else:
            logger.error("❌ 数据处理失败")

        # 步骤5: 清理临时文件
        logger.info("步骤5: 清理临时文件...")
        downloader.cleanup_temp_files()

    except KeyboardInterrupt:
        logger.info("用户中断下载过程")
        downloader.cleanup_temp_files()
    except Exception as e:
        logger.error(f"运行过程中出错: {e}")
        downloader.cleanup_temp_files()

if __name__ == "__main__":
    main()
