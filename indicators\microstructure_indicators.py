#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
市场微观结构指标实现

为WaterFallAlpha策略提供VPIN和Roll Measure等高级微观结构指标。
这些指标用于量化市场的"毒性"和信息不对称程度。
"""

import numpy as np
import pandas as pd
from typing import Optional, Tuple, Dict, Any
import logging
from scipy import stats

from .base import Indicator
from .utils.validation import validate_data


class VPINIndicator(Indicator):
    """
    VPIN (Volume-Synchronized Probability of Informed Trading) 指标

    VPIN衡量订单流的"毒性"或信息不对称程度。高VPIN值意味着市场中存在
    大量知情交易，或者在清算事件中，是由强制清算引起的极端订单流不平衡。
    """

    def __init__(self, window: int = 50, volume_bucket_size: Optional[float] = None):
        """
        初始化VPIN指标

        Args:
            window: 计算窗口大小(时间条数)
            volume_bucket_size: 成交量桶大小，None表示自动计算
        """
        super().__init__(name="VPIN", category="microstructure")
        self.window = window
        self.volume_bucket_size = volume_bucket_size
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        计算VPIN指标
        
        Args:
            data: 包含价格和成交量数据的DataFrame
                 需要包含: 'close', 'volume', 'high', 'low'
                 
        Returns:
            VPIN值序列
        """
        validate_data(data, required_columns=['close', 'volume', 'high', 'low'])
        
        # 计算价格变化
        price_changes = data['close'].diff()
        
        # 估算买卖成交量 (使用价格变化方向)
        buy_volume = np.where(price_changes > 0, data['volume'], 0)
        sell_volume = np.where(price_changes < 0, data['volume'], 0)
        
        # 对于价格不变的情况，使用高低价差分配
        unchanged_mask = price_changes == 0
        hl_ratio = (data['close'] - data['low']) / (data['high'] - data['low'])
        hl_ratio = hl_ratio.fillna(0.5)  # 默认50-50分配
        
        buy_volume = np.where(unchanged_mask, data['volume'] * hl_ratio, buy_volume)
        sell_volume = np.where(unchanged_mask, data['volume'] * (1 - hl_ratio), sell_volume)
        
        # 创建成交量桶
        if self.volume_bucket_size is None:
            # 自动计算桶大小：使用平均成交量
            self.volume_bucket_size = data['volume'].mean()
            
        # 计算VPIN
        vpin_values = []
        
        for i in range(len(data)):
            if i < self.window:
                vpin_values.append(np.nan)
                continue
                
            # 获取窗口内数据
            window_start = max(0, i - self.window + 1)
            window_buy = buy_volume[window_start:i+1]
            window_sell = sell_volume[window_start:i+1]
            window_total = data['volume'].iloc[window_start:i+1]
            
            # 计算VPIN
            total_volume = window_total.sum()
            if total_volume == 0:
                vpin_values.append(0)
                continue
                
            volume_imbalance = abs(window_buy.sum() - window_sell.sum())
            vpin = volume_imbalance / total_volume
            vpin_values.append(vpin)
            
        return pd.Series(vpin_values, index=data.index, name='VPIN')
        
    def get_signal_thresholds(self) -> Dict[str, float]:
        """获取VPIN信号阈值"""
        return {
            'low_toxicity': 0.3,
            'medium_toxicity': 0.45,
            'high_toxicity': 0.6,
            'extreme_toxicity': 0.8
        }


class RollMeasureIndicator(Indicator):
    """
    Roll Measure 指标

    通过价格变化的序列协方差来估算有效的买卖价差。
    高Roll Measure值与动量交易行为高度相关。
    """

    def __init__(self, window: int = 50):
        """
        初始化Roll Measure指标

        Args:
            window: 计算窗口大小
        """
        super().__init__(name="Roll_Measure", category="microstructure")
        self.window = window
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        计算Roll Measure指标
        
        Args:
            data: 包含价格数据的DataFrame，需要包含'close'列
                 
        Returns:
            Roll Measure值序列
        """
        validate_data(data, required_columns=['close'])
        
        # 计算价格变化
        price_changes = data['close'].diff()
        
        # 计算滚动协方差
        roll_values = []
        
        for i in range(len(data)):
            if i < self.window:
                roll_values.append(np.nan)
                continue
                
            # 获取窗口内价格变化
            window_changes = price_changes.iloc[max(0, i - self.window + 1):i+1]
            
            if len(window_changes) < 2:
                roll_values.append(0)
                continue
                
            # 计算序列协方差 Cov(ΔPt, ΔPt-1)
            changes_current = window_changes.iloc[1:]
            changes_lagged = window_changes.iloc[:-1]
            
            if len(changes_current) == 0 or len(changes_lagged) == 0:
                roll_values.append(0)
                continue
                
            covariance = np.cov(changes_current, changes_lagged)[0, 1]
            
            # Roll Measure = 2 * sqrt(-Cov(ΔPt, ΔPt-1))
            # 注意：协方差通常为负值，所以取负号
            if covariance < 0:
                roll_measure = 2 * np.sqrt(-covariance)
            else:
                roll_measure = 0
                
            roll_values.append(roll_measure)
            
        return pd.Series(roll_values, index=data.index, name='Roll_Measure')
        
    def get_signal_thresholds(self) -> Dict[str, float]:
        """获取Roll Measure信号阈值"""
        return {
            'low_momentum': 0.001,
            'medium_momentum': 0.005,
            'high_momentum': 0.01,
            'extreme_momentum': 0.02
        }


class MicrostructureAnalyzer:
    """
    微观结构分析器
    
    综合分析VPIN和Roll Measure指标，为WaterFallAlpha策略提供
    市场微观结构状态评估。
    """
    
    def __init__(self, vpin_window: int = 50, roll_window: int = 50):
        """
        初始化微观结构分析器
        
        Args:
            vpin_window: VPIN计算窗口
            roll_window: Roll Measure计算窗口
        """
        self.vpin_indicator = VPINIndicator(window=vpin_window)
        self.roll_indicator = RollMeasureIndicator(window=roll_window)
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def analyze(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        综合分析市场微观结构
        
        Args:
            data: 市场数据DataFrame
            
        Returns:
            包含微观结构指标的DataFrame
        """
        result = data.copy()
        
        # 计算VPIN
        result['vpin'] = self.vpin_indicator.calculate(data)
        
        # 计算Roll Measure
        result['roll_measure'] = self.roll_indicator.calculate(data)
        
        # 计算综合毒性评分
        result['toxicity_score'] = self._calculate_toxicity_score(
            result['vpin'], result['roll_measure']
        )
        
        # 生成信号
        result['microstructure_signal'] = self._generate_signals(result)
        
        return result
        
    def _calculate_toxicity_score(self, vpin: pd.Series, roll_measure: pd.Series) -> pd.Series:
        """
        计算综合毒性评分
        
        Args:
            vpin: VPIN值序列
            roll_measure: Roll Measure值序列
            
        Returns:
            毒性评分序列 (0-1之间)
        """
        # 标准化VPIN (0-1范围)
        vpin_normalized = np.clip(vpin, 0, 1)
        
        # 标准化Roll Measure
        roll_max = roll_measure.quantile(0.95)  # 使用95分位数作为最大值
        roll_normalized = np.clip(roll_measure / roll_max, 0, 1)
        
        # 加权组合 (VPIN权重更高，因为它更直接反映信息不对称)
        toxicity_score = 0.7 * vpin_normalized + 0.3 * roll_normalized
        
        return toxicity_score
        
    def _generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """
        生成微观结构信号
        
        Args:
            data: 包含微观结构指标的DataFrame
            
        Returns:
            信号序列 (-1: 高毒性, 0: 正常, 1: 低毒性)
        """
        signals = pd.Series(0, index=data.index)
        
        # 获取阈值
        vpin_thresholds = self.vpin_indicator.get_signal_thresholds()
        
        # 高毒性信号 (适合瀑布猎人策略)
        high_toxicity_mask = (
            (data['vpin'] > vpin_thresholds['high_toxicity']) |
            (data['toxicity_score'] > 0.7)
        )
        signals[high_toxicity_mask] = -1
        
        # 低毒性信号
        low_toxicity_mask = (
            (data['vpin'] < vpin_thresholds['low_toxicity']) &
            (data['toxicity_score'] < 0.3)
        )
        signals[low_toxicity_mask] = 1
        
        return signals
        
    def detect_cascade_conditions(self, data: pd.DataFrame) -> pd.Series:
        """
        检测清算瀑布条件
        
        Args:
            data: 包含微观结构指标的DataFrame
            
        Returns:
            瀑布条件检测结果 (True/False)
        """
        # 瀑布条件：
        # 1. VPIN快速上升
        # 2. Roll Measure显著增加
        # 3. 毒性评分超过阈值
        
        vpin_rising = data['vpin'].diff() > 0.1  # VPIN快速上升
        roll_high = data['roll_measure'] > data['roll_measure'].rolling(20).mean() * 2
        toxicity_extreme = data['toxicity_score'] > 0.8
        
        cascade_conditions = vpin_rising & roll_high & toxicity_extreme
        
        return cascade_conditions
        
    def get_market_state(self, current_data: pd.Series) -> Dict[str, Any]:
        """
        获取当前市场微观结构状态
        
        Args:
            current_data: 当前时刻的数据
            
        Returns:
            市场状态字典
        """
        vpin_thresholds = self.vpin_indicator.get_signal_thresholds()
        roll_thresholds = self.roll_indicator.get_signal_thresholds()
        
        vpin_value = current_data.get('vpin', 0)
        roll_value = current_data.get('roll_measure', 0)
        toxicity_value = current_data.get('toxicity_score', 0)
        
        # 确定VPIN状态
        if vpin_value > vpin_thresholds['extreme_toxicity']:
            vpin_state = 'extreme_toxicity'
        elif vpin_value > vpin_thresholds['high_toxicity']:
            vpin_state = 'high_toxicity'
        elif vpin_value > vpin_thresholds['medium_toxicity']:
            vpin_state = 'medium_toxicity'
        else:
            vpin_state = 'low_toxicity'
            
        # 确定Roll Measure状态
        if roll_value > roll_thresholds['extreme_momentum']:
            roll_state = 'extreme_momentum'
        elif roll_value > roll_thresholds['high_momentum']:
            roll_state = 'high_momentum'
        elif roll_value > roll_thresholds['medium_momentum']:
            roll_state = 'medium_momentum'
        else:
            roll_state = 'low_momentum'
            
        return {
            'vpin_value': vpin_value,
            'vpin_state': vpin_state,
            'roll_value': roll_value,
            'roll_state': roll_state,
            'toxicity_score': toxicity_value,
            'cascade_risk': toxicity_value > 0.7,
            'market_stress': vpin_value > vpin_thresholds['high_toxicity'] and roll_value > roll_thresholds['high_momentum']
        }
