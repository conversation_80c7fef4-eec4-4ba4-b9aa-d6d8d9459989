#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ADL-Anticipation策略：利用币安期货市场微观结构无效率

基于ADL策略文档，实现预测自动减仓（ADL）事件的5分钟交易策略。
核心论点：通过预测ADL事件的发生，抢先交易由强制平仓引起的价格瞬间波动。

策略特点：
1. 基于"准-ADL"复合指标：ATR波动性扩张 + Z-Score价格偏离
2. 专注USDC-M合约的0%挂单费率优势
3. 严格的1.5:1风险回报比和基于ATR的动态止损
4. 集成FreqTrade专业风险保护系统
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Optional, Union

from freqtrade.strategy import IStrategy, Trade
from freqtrade.strategy import CategoricalParameter
from freqtrade.persistence import Order
from pandas import DataFrame
import talib.abstract as ta




class ADLAnticipationStrategy(IStrategy):
    """
    ADL-Anticipation策略
    
    基于ADL策略文档第3节的完整实现：
    - 交易对动态筛选：选择波动性与流动性的"甜蜜点"
    - "准-ADL"信号生成：ATR扩张 + Z-Score偏离的复合指标
    - 挂单执行：永远做Maker，利用USDC-M的0%费率优势
    - 风险管理：基于ATR的动态止盈止损，1.5:1风险回报比
    """
    
    # 策略元数据
    INTERFACE_VERSION = 3
    timeframe = '5m'  # 严格按照ADL文档要求的5分钟时间框架
    startup_candle_count: int = 60  # 需要60根K线来计算指标
    max_open_trades = 5  # 强制设置最大持仓数
    
    # ROI表：从配置文件加载，支持动态调整
    # 默认值作为备用，优先使用配置文件中的设置
    minimal_roi = {
        "0": 0.025,   # 默认立即2.5%止盈
        "10": 0.012,  # 默认10分钟后1.2%
        "20": 0.006,  # 默认20分钟后0.6%
        "30": 0       # 默认30分钟后0%
    }

    stoploss = -0.080  # 8.0%固定止损，回滚到基线：1.8-3倍杠杆下相当于-2.7%到-4.4%价格变动

    # 动态止盈配置
    use_custom_stoploss = False  # 禁用动态止盈功能，专注基础参数优化

    # 🎯 ADL策略参数 - 从配置文件动态加载
    # 参数现在在config_adl_backtest.json的adl_strategy_params部分定义
    # 这样可以方便地进行参数测试而无需修改策略代码



    # 执行优化参数
    maker_only = CategoricalParameter([True, False], default=True, space="buy")  # 启用挂单模式（利用0%费率）
    
    def __init__(self, config: dict) -> None:
        super().__init__(config)
        self.logger = logging.getLogger(self.__class__.__name__)

        # 从配置文件读取ADL策略参数 - 严格模式，不提供默认值
        adl_params = config['adl_strategy_params']

        # 核心参数 - 必须在配置文件中定义
        self.atr_period = adl_params['atr_period']
        self.atr_sma_period = adl_params['atr_sma_period']
        self.zscore_period = adl_params['zscore_period']

        # 可优化参数 - 必须在配置文件中定义
        self.atr_multiplier = adl_params['atr_multiplier']
        self.zscore_threshold = adl_params['zscore_threshold']
        self.atr_profit_multiplier = adl_params['atr_profit_multiplier']

        # 风险管理参数
        self.atr_stop_multiplier = adl_params['atr_stop_multiplier']

        # 🚀 第三步优化参数加载 - 严格从配置读取
        step3_config = adl_params['step3_optimization']
        self.step3_enabled = step3_config['enable']
        
        if self.step3_enabled:
            # 信号强度分层系统
            tier_config = step3_config['signal_tier_system']
            self.tier1_strength_min = tier_config['tier1_strength_min']
            self.tier2_strength_min = tier_config['tier2_strength_min']
            self.tier3_strength_min = tier_config['tier3_strength_min']
            self.tier1_max_trades = tier_config['tier1_max_trades']
            self.tier2_max_trades = tier_config['tier2_max_trades']
            self.tier3_max_trades = tier_config['tier3_max_trades']
            
            # 激进时间管理
            time_config = step3_config['aggressive_time_management']
            self.aggressive_time_enabled = time_config['enable']
            self.tier1_time_limits = time_config['tier1_time_limits']
            self.tier2_time_limits = time_config['tier2_time_limits']
            self.tier3_time_limits = time_config['tier3_time_limits']
            self.profit_protection_time = time_config['profit_protection_time']
            
            # 价格动量确认
            momentum_config = step3_config['momentum_confirmation']
            self.momentum_confirmation_enabled = momentum_config['enable']
            self.price_acceleration_periods = momentum_config['price_acceleration_periods']
            self.min_acceleration_ratio = momentum_config['min_acceleration_ratio']
            self.volume_burst_multiplier = momentum_config['volume_burst_multiplier']
            # RSI阈值设置
            self.rsi_oversold_threshold = momentum_config['rsi_oversold_threshold']
            self.rsi_overbought_threshold = momentum_config['rsi_overbought_threshold']
            
            # 盈利保护机制
            profit_config = step3_config['profit_protection']
            self.profit_protection_enabled = profit_config['enable']
            self.min_profit_for_protection = profit_config['min_profit_for_protection']
            self.protected_time_limit = profit_config['protected_time_limit']
            self.quick_exit_loss_threshold = profit_config['quick_exit_loss_threshold']
            
            # 信号质量控制
            quality_config = step3_config['signal_quality_control']
            self.consecutive_loss_limit = quality_config['consecutive_loss_limit']
            self.daily_signal_limit = quality_config['daily_signal_limit']
            self.cooldown_after_loss = quality_config['cooldown_after_loss']
            
            self.logger.info("🚀 第三步优化已启用：信号分层 + 激进时间管理 + 动量确认")
        else:
            # 第三步优化未启用时，仍然需要读取RSI阈值
            momentum_config = adl_params['step3_optimization']['momentum_confirmation']
            self.rsi_oversold_threshold = momentum_config['rsi_oversold_threshold']
            self.rsi_overbought_threshold = momentum_config['rsi_overbought_threshold']
            self.logger.info("📊 使用第二步优化配置（第三步优化未启用）")

        # ROI配置：从配置文件加载
        config_roi = adl_params['minimal_roi']
        self.minimal_roi = config_roi
        self.logger.info(f"📊 从配置加载ROI表: {config_roi}")

        # 执行参数
        self.maker_only = adl_params['maker_only']

        # 🔧 加载基础阈值配置 - 消除硬编码
        basic_config = adl_params['basic_thresholds']
        self.strong_signal_threshold = basic_config['strong_signal_threshold']
        self.high_quality_signal_threshold = basic_config['high_quality_signal_threshold']
        self.signal_strength_decline_threshold = basic_config['signal_strength_decline_threshold']
        self.default_fillna_value = basic_config['default_fillna_value']

        # 🔧 加载ATR波动率级别配置
        atr_vol_config = adl_params['atr_volatility_levels']
        self.atr_very_high_threshold = atr_vol_config['very_high_threshold']
        self.atr_high_threshold = atr_vol_config['high_threshold']
        self.atr_medium_threshold = atr_vol_config['medium_threshold']
        self.atr_low_threshold = atr_vol_config['low_threshold']
        self.atr_default_ratio = atr_vol_config['default_ratio']

        # 🔧 加载盈利/亏损阈值配置
        profit_loss_config = adl_params['profit_loss_thresholds']
        self.quick_loss_threshold = profit_loss_config['quick_loss_threshold']
        self.profit_protection_threshold = profit_loss_config['profit_protection_threshold']
        self.min_profit_for_signal_tracking = profit_loss_config['min_profit_for_signal_tracking']
        self.max_loss_for_position_adjustment = profit_loss_config['max_loss_for_position_adjustment']

        # 🔧 加载波动率自适应倍数配置
        vol_multiplier_config = adl_params['volatility_adaptive_multipliers']
        self.vol_high_threshold = vol_multiplier_config['high_volatility_threshold']
        self.vol_high_multiplier = vol_multiplier_config['high_volatility_multiplier']
        self.vol_medium_threshold = vol_multiplier_config['medium_volatility_threshold']
        self.vol_medium_multiplier = vol_multiplier_config['medium_volatility_multiplier']
        self.vol_low_multiplier = vol_multiplier_config['low_volatility_multiplier']
        self.pos_vol_high_threshold = vol_multiplier_config['position_high_volatility_threshold']
        self.pos_vol_high_multiplier = vol_multiplier_config['position_high_volatility_multiplier']
        self.pos_vol_medium_threshold = vol_multiplier_config['position_medium_volatility_threshold']
        self.pos_vol_medium_multiplier = vol_multiplier_config['position_medium_volatility_multiplier']
        self.pos_vol_low_multiplier = vol_multiplier_config['position_low_volatility_multiplier']

        # 🔧 加载仓位调整配置
        pos_adj_config = adl_params['position_adjustment']
        self.tier1_min_signal_strength = pos_adj_config['tier1_min_signal_strength']
        self.tier1_position_multiplier = pos_adj_config['tier1_position_multiplier']
        self.tier1_max_additions = pos_adj_config['tier1_max_additions']
        self.tier2_min_signal_strength = pos_adj_config['tier2_min_signal_strength']
        self.tier2_position_multiplier = pos_adj_config['tier2_position_multiplier']
        self.tier2_max_additions = pos_adj_config['tier2_max_additions']
        self.tier3_min_signal_strength = pos_adj_config['tier3_min_signal_strength']
        self.tier3_position_multiplier = pos_adj_config['tier3_position_multiplier']
        self.tier3_max_additions = pos_adj_config['tier3_max_additions']
        self.default_min_signal_strength = pos_adj_config['default_min_signal_strength']
        self.default_position_multiplier = pos_adj_config['default_position_multiplier']
        self.default_max_additions = pos_adj_config['default_max_additions']
        self.pos_adj_min_interval_minutes = pos_adj_config['min_interval_minutes']

        # 🔧 加载杠杆配置
        leverage_config = adl_params['leverage_settings']
        self.fallback_leverage_step3_disabled = leverage_config['fallback_leverage_step3_disabled']
        self.fallback_leverage_no_tag = leverage_config['fallback_leverage_no_tag']

        # 🔧 加载价格调整配置
        price_adj_config = adl_params['price_adjustment']
        self.atr_adjustment_ratio = price_adj_config['atr_adjustment_ratio']
        self.max_adjustment_long = price_adj_config['max_adjustment_long']
        self.max_adjustment_short = price_adj_config['max_adjustment_short']

        # 🔧 加载leverage_revolution配置参数 - 严格从配置读取
        leverage_config = adl_params['leverage_revolution']
        self.leverage_revolution_enabled = leverage_config['enable']
        
        if self.leverage_revolution_enabled:
            # 严格检查所有必需参数，不使用默认值
            self.tier1_leverage = leverage_config['tier1_leverage']
            self.tier2_leverage = leverage_config['tier2_leverage'] 
            self.tier3_leverage = leverage_config['tier3_leverage']
            self.base_leverage = leverage_config['base_leverage']
            # 可选参数仍可保留默认值
            self.volatility_adjustment = leverage_config.get('volatility_adjustment', True)
            self.max_leverage_config = leverage_config.get('max_leverage', 10)
            self.min_leverage_config = leverage_config.get('min_leverage', 1)
            self.logger.info(f"🚀 加载leverage_revolution配置: T1={self.tier1_leverage}x, T2={self.tier2_leverage}x, T3={self.tier3_leverage}x")
        else:
            raise ValueError("❌ leverage_revolution 未启用！请在配置文件中启用或移除此功能。")

        # 🚀 多仓位优化配置加载 - 严格从配置读取
        multi_pos_config = adl_params['multi_position_optimization']
        self.multi_position_enabled = multi_pos_config['enable']
        
        if self.multi_position_enabled:
            # 时间间隔配置
            time_config = multi_pos_config['time_intervals']
            self.tier1_min_interval = time_config['tier1_min_minutes']
            self.tier2_min_interval = time_config['tier2_min_minutes'] 
            self.tier3_min_interval = time_config['tier3_min_minutes']
            
            # 仓位限制配置
            limit_config = multi_pos_config['position_limits']
            self.max_same_direction = limit_config['max_same_direction']
            self.max_same_tier = limit_config['max_same_tier']
            self.max_total_positions = limit_config['max_total_positions']
            
            # 质量要求配置
            quality_config = multi_pos_config['quality_requirements']
            self.tier1_only_when_crowded = quality_config['tier1_only_when_crowded']
            self.signal_upgrade_ratio = quality_config['signal_upgrade_ratio']
            
            self.logger.info(f"🚀 多仓位优化已启用: 时间间隔T1={self.tier1_min_interval}min, "
                           f"仓位限制={self.max_same_direction}/{self.max_total_positions}, "
                           f"信号升级比率={self.signal_upgrade_ratio}")
        else:
            self.logger.info("📊 多仓位优化未启用，使用标准模式")

        # 🔧 加载成交量确认配置
        volume_config = adl_params['volume_confirmation']
        self.volume_sma_period = volume_config['volume_sma_period']
        self.volume_multiplier = volume_config['volume_multiplier']
        self.enable_volume_filter = volume_config['enable_volume_filter']
        self.logger.info(f"🔧 成交量确认配置: 周期={self.volume_sma_period}, 倍数={self.volume_multiplier}, 启用={self.enable_volume_filter}")

        # 🚀 信号回收系统配置加载 - 严格从配置读取
        signal_recovery_config = adl_params['signal_recovery_system']
        self.signal_recovery_enabled = signal_recovery_config['enable']
        
        if self.signal_recovery_enabled:
            # 优先信号强度阈值
            self.priority_signal_strength = signal_recovery_config['priority_signal_strength']
            
            # 动态时间调整配置
            time_adj_config = signal_recovery_config['dynamic_time_adjustment']
            self.dynamic_time_enabled = time_adj_config['enable']
            self.high_strength_reduction = time_adj_config['high_strength_reduction']
            self.signal_density_threshold = time_adj_config['signal_density_threshold']
            self.low_density_relaxation = time_adj_config['low_density_relaxation']
            
            # 仓位替换配置
            replacement_config = signal_recovery_config['position_replacement']
            self.position_replacement_enabled = replacement_config['enable']
            self.min_quality_gap = replacement_config['min_quality_gap']
            self.replacement_time_limit = replacement_config['replacement_time_limit']
            
            # 被拒绝信号追踪配置
            tracking_config = signal_recovery_config['rejected_signal_tracking']
            self.rejected_signal_tracking = tracking_config['enable']
            
            # 初始化信号追踪
            self.rejected_signals = []
            self.signal_density_history = []
            
            self.logger.info(f"🚀 信号回收系统已启用 - 优先强度阈值: {self.priority_signal_strength}")
        else:
            self.signal_recovery_enabled = False
            self.logger.info("📊 信号回收系统未启用")

        self.logger.info("🔧 从配置文件加载ADL参数")

        # 策略状态跟踪
        self.last_signal_time = {}  # 记录最后信号时间，避免频繁交易
        self.consecutive_losses = 0  # 连续亏损计数
        self.daily_signal_count = 0  # 日内信号计数
        self.last_trade_date = None  # 上次交易日期

        # 参数诊断
        self._diagnose_parameters()

        self.logger.info("🚀 ADL-Anticipation策略初始化完成")
        self.logger.info(f"📊 目标交易对：USDC-M/USDT-M合约（0%挂单费率）")
        self.logger.info(f"⚡ 时间框架：{self.timeframe}")
        self.logger.info(f"🎯 频率优化：降低阈值增加2倍交易频率，提升杠杆增强盈利")
        self.logger.info(f"🔧 配置状态：无硬编码，全参数可调节")

    def _diagnose_parameters(self):
        """诊断参数加载情况"""
        self.logger.info("🔍 === 参数诊断 ===")
        self.logger.info(f"📊 ATR倍数: {self.atr_multiplier}")
        self.logger.info(f"📊 Z-Score阈值: {self.zscore_threshold}")
        self.logger.info(f"📊 ATR周期: {self.atr_period}")
        self.logger.info(f"📊 ATR SMA周期: {self.atr_sma_period}")
        self.logger.info(f"📊 Z-Score周期: {self.zscore_period}")
        self.logger.info(f"📊 Max Open Trades: {getattr(self, 'max_open_trades', 'undefined')}")
        self.logger.info(f"📊 Maker Only: {self.maker_only}")
        
        # 新增：信号分层阈值诊断
        if self.step3_enabled:
            self.logger.info("🎯 === 信号分层阈值 ===")
            self.logger.info(f"🥇 Tier1 (顶级): ≥{self.tier1_strength_min} (预期：少量高质量信号)")
            self.logger.info(f"🥈 Tier2 (优质): {self.tier2_strength_min}-{self.tier1_strength_min} (预期：中等数量优质信号)")
            self.logger.info(f"🥉 Tier3 (基础): {self.tier3_strength_min}-{self.tier2_strength_min} (预期：适量基础信号)")
            self.logger.info(f"🚫 过滤阈值: <{self.tier3_strength_min} (预期：过滤噪音信号)")
            self.logger.info("🎯 === 分层逻辑完成 ===")
        
        self.logger.info("🔍 === 诊断完成 ===")

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充技术指标
        
        基于ADL策略文档第3.2节，实现"准-ADL"复合指标计算
        """
        pair = metadata.get('pair', 'UNKNOWN')
        self.logger.info(f"🔄 计算ADL指标 - {pair}")
        
        # 🔧 参数确认：显示实际使用的参数值
        if 'BTC/' in pair and ('USDC' in pair or 'USDT' in pair):  # 只对BTC输出，避免日志过多
            self.logger.info(f"🔧 关键参数确认: ATR倍数={self.atr_multiplier}, Z-Score阈值={self.zscore_threshold}")
        
        # 使用内置ADL计算，确保参数一致性
        dataframe = self._calculate_adl_indicators_builtin(dataframe)
            
        # 基础技术指标
        dataframe['rsi'] = ta.RSI(dataframe['close'], timeperiod=14)
        dataframe['ema_20'] = ta.EMA(dataframe['close'], timeperiod=20)

        # 趋势判断指标（用于分层止损）
        dataframe['sma_20'] = ta.SMA(dataframe['close'], timeperiod=20)
        dataframe['sma_50'] = ta.SMA(dataframe['close'], timeperiod=50)

        # 🔧 成交量确认 - 使用配置参数
        dataframe['volume_sma'] = dataframe['volume'].rolling(window=self.volume_sma_period).mean()
        if self.enable_volume_filter:
            dataframe['volume_confirm'] = dataframe['volume'] > (dataframe['volume_sma'] * self.volume_multiplier)
        else:
            # 如果禁用成交量过滤，所有信号都通过
            dataframe['volume_confirm'] = True
        
        # 🚀 第三步优化：价格动量确认指标
        if self.step3_enabled and self.momentum_confirmation_enabled:
            # 价格加速度检测（3周期价格变化率的变化率）
            dataframe['price_change_1'] = dataframe['close'].pct_change(1)
            dataframe['price_change_2'] = dataframe['close'].pct_change(2) 
            dataframe['price_change_3'] = dataframe['close'].pct_change(3)
            
            # 计算价格加速度（变化率的增加趋势）
            dataframe['price_acceleration'] = (
                abs(dataframe['price_change_1']) / 
                (abs(dataframe['price_change_2']) + 1e-8)
            ).fillna(self.default_fillna_value)
            
            # 成交量爆发检测
            dataframe['volume_ratio'] = (
                dataframe['volume'] / dataframe['volume_sma']
            ).fillna(self.default_fillna_value)
            
            # 动量确认条件
            dataframe['momentum_confirmed'] = (
                (dataframe['price_acceleration'] > self.min_acceleration_ratio) &
                (dataframe['volume_ratio'] > self.volume_burst_multiplier)
            )
            
            self.logger.info(f"📈 动量确认指标已计算 - {pair}: 加速度阈值{self.min_acceleration_ratio}, 成交量倍数{self.volume_burst_multiplier}")
        else:
            # 第三步优化未启用时，默认所有信号都通过动量确认
            dataframe['momentum_confirmed'] = True
        
        return dataframe
        
    def _calculate_adl_indicators_builtin(self, dataframe: DataFrame) -> DataFrame:
        """
        内置ADL指标计算（备用方案）

        实现与ADLAnticipationIndicator相同的逻辑
        支持hyperopt参数优化
        """
        # 🔧 修复：强制使用配置文件参数（而非hyperopt默认值）
        # 在普通回测中，直接使用config文件加载的参数
        atr_multiplier = self.atr_multiplier
        zscore_threshold = self.zscore_threshold
        


        # 1. ATR波动性扩张检测（与文档伪代码保持一致）
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period)
        dataframe['sma_atr'] = ta.SMA(dataframe['atr'], timeperiod=self.atr_sma_period)
        dataframe['atr_expansion'] = dataframe['atr'] > (dataframe['sma_atr'] * atr_multiplier)

        # 2. Z-Score价格偏离检测
        dataframe['sma'] = dataframe['close'].rolling(window=self.zscore_period).mean()
        dataframe['stddev'] = dataframe['close'].rolling(window=self.zscore_period).std()
        dataframe['zscore'] = (dataframe['close'] - dataframe['sma']) / dataframe['stddev']

        dataframe['zscore_extreme_low'] = dataframe['zscore'] < -zscore_threshold
        dataframe['zscore_extreme_high'] = dataframe['zscore'] > zscore_threshold
        
        # 3. "准-ADL"复合信号
        dataframe['adl_long_signal'] = dataframe['zscore_extreme_low'] & dataframe['atr_expansion']
        dataframe['adl_short_signal'] = dataframe['zscore_extreme_high'] & dataframe['atr_expansion']
        
        # 4. 信号强度（修复计算逻辑，支持hyperopt优化）
        dataframe['adl_signal_strength'] = (
            (np.abs(dataframe['zscore']) / zscore_threshold) *
            (dataframe['atr'] / dataframe['sma_atr']) / atr_multiplier
        ).clip(0, 2)  # 允许超过1的强度值
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充入场信号
        
        🚀 第三步优化：实现信号强度分层机制
        - 顶级信号(>2.0)：最高优先级，正常交易
        - 二级信号(1.5-2.0)：中等优先级，限制交易数量
        - 三级信号(1.2-1.5)：低优先级，严格限制
        """
        pair = metadata.get('pair', 'UNKNOWN')
        
        # 基础ADL信号
        long_adl_signal = dataframe['adl_long_signal'] == True
        short_adl_signal = dataframe['adl_short_signal'] == True

        # 🔧 第四步优化：放宽信号质量过滤器以提高频率
        volume_filter = dataframe['volume_confirm'] == True
        rsi_oversold = dataframe['rsi'] < self.rsi_oversold_threshold  # 从配置文件读取
        rsi_overbought = dataframe['rsi'] > self.rsi_overbought_threshold  # 从配置文件读取
        
        # 🚀 第三步优化：信号强度分层和动量确认
        if self.step3_enabled:
            # 信号强度分层
            signal_strength = dataframe['adl_signal_strength']
            tier1_strength = signal_strength > self.tier1_strength_min  # >2.0 顶级信号
            tier2_strength = (signal_strength > self.tier2_strength_min) & (signal_strength <= self.tier1_strength_min)  # 1.5-2.0
            tier3_strength = (signal_strength > self.tier3_strength_min) & (signal_strength <= self.tier2_strength_min)  # 1.2-1.5
            
            # 动量确认
            momentum_confirmed = dataframe['momentum_confirmed'] == True
            
            # 分层入场条件
            # 顶级信号：最严格的质量要求
            tier1_long_condition = (
                long_adl_signal & 
                volume_filter & 
                rsi_oversold & 
                tier1_strength & 
                momentum_confirmed
            )
            
            tier1_short_condition = (
                short_adl_signal & 
                volume_filter & 
                rsi_overbought & 
                tier1_strength & 
                momentum_confirmed
            )
            
            # 二级信号：中等要求，可选择性放宽某些条件
            tier2_long_condition = (
                long_adl_signal & 
                volume_filter & 
                (rsi_oversold | (dataframe['rsi'] < self.rsi_oversold_threshold - 10)) &  # 基于配置值放宽RSI要求
                tier2_strength & 
                momentum_confirmed
            )
            
            tier2_short_condition = (
                short_adl_signal & 
                volume_filter & 
                (rsi_overbought | (dataframe['rsi'] > self.rsi_overbought_threshold + 10)) &  # 基于配置值放宽RSI要求
                tier2_strength & 
                momentum_confirmed
            )
            
            # 三级信号：基本要求，但数量严格限制
            tier3_long_condition = (
                long_adl_signal & 
                volume_filter & 
                tier3_strength & 
                momentum_confirmed  # 仍需动量确认
            )
            
            tier3_short_condition = (
                short_adl_signal & 
                volume_filter & 
                tier3_strength & 
                momentum_confirmed
            )
            
            # 信号标记：为不同等级的信号设置不同的entry_tag
            dataframe.loc[tier1_long_condition, 'enter_long'] = 1
            dataframe.loc[tier1_long_condition, 'enter_tag'] = 'tier1_long'
            
            dataframe.loc[tier1_short_condition, 'enter_short'] = 1  
            dataframe.loc[tier1_short_condition, 'enter_tag'] = 'tier1_short'
            
            dataframe.loc[tier2_long_condition, 'enter_long'] = 1
            dataframe.loc[tier2_long_condition, 'enter_tag'] = 'tier2_long'
            
            dataframe.loc[tier2_short_condition, 'enter_short'] = 1
            dataframe.loc[tier2_short_condition, 'enter_tag'] = 'tier2_short'
            
            dataframe.loc[tier3_long_condition, 'enter_long'] = 1
            dataframe.loc[tier3_long_condition, 'enter_tag'] = 'tier3_long'
            
            dataframe.loc[tier3_short_condition, 'enter_short'] = 1
            dataframe.loc[tier3_short_condition, 'enter_tag'] = 'tier3_short'
            
            # 统计各层级信号
            tier1_signals = tier1_long_condition.sum() + tier1_short_condition.sum()
            tier2_signals = tier2_long_condition.sum() + tier2_short_condition.sum()
            tier3_signals = tier3_long_condition.sum() + tier3_short_condition.sum()
            total_signals = tier1_signals + tier2_signals + tier3_signals
            
            # 🔍 可解释性增强：计算过滤统计
            total_adl_signals = long_adl_signal.sum() + short_adl_signal.sum()
            total_volume_passed = volume_filter.sum()
            total_rsi_passed = (rsi_oversold.sum() + rsi_overbought.sum())
            total_momentum_passed = momentum_confirmed.sum()
            total_above_tier3 = (signal_strength > self.tier3_strength_min).sum()
            filtered_out = total_adl_signals - total_signals
            
            self.logger.info(f"🎯 分层信号统计 - {pair}: T1={tier1_signals}, T2={tier2_signals}, T3={tier3_signals}, 总计={total_signals}")
            
            # 🔍 详细过滤统计（只对主要币种显示，避免日志过多）
            if 'BTC/' in pair or 'ETH/' in pair:
                filter_ratio = (total_signals / total_adl_signals * 100) if total_adl_signals > 0 else 0
                self.logger.info(f"📊 过滤统计 - {pair}:")
                self.logger.info(f"  └ 原始ADL信号: {total_adl_signals}")
                self.logger.info(f"  └ 强度≥{self.tier3_strength_min}: {total_above_tier3}")
                self.logger.info(f"  └ 成交量通过: {total_volume_passed}")
                self.logger.info(f"  └ RSI通过: {total_rsi_passed}")  
                self.logger.info(f"  └ 动量通过: {total_momentum_passed}")
                self.logger.info(f"  └ 最终信号: {total_signals} ({filter_ratio:.1f}%通过率)")
                self.logger.info(f"  └ 被过滤: {filtered_out} (噪音信号)")
        
        else:
            # 第四步优化：降低信号强度门槛以提高频率
            strong_signal = dataframe['adl_signal_strength'] > self.strong_signal_threshold
            
            long_entry_condition = (
                long_adl_signal & 
                volume_filter & 
                rsi_oversold & 
                strong_signal
            )

            short_entry_condition = (
                short_adl_signal & 
                volume_filter & 
                rsi_overbought & 
                strong_signal
            )
            
            dataframe.loc[long_entry_condition, 'enter_long'] = 1
            dataframe.loc[short_entry_condition, 'enter_short'] = 1
            
            total_signals = long_entry_condition.sum() + short_entry_condition.sum()
            self.logger.info(f"🎯 传统信号统计 - {pair}: 总计={total_signals}")
        
        # 基础统计信息（保持向后兼容）
        adl_long_count = long_adl_signal.sum()
        adl_short_count = short_adl_signal.sum()
        
        self.logger.info(f"🔍 ADL信号检测 - {pair}: ADL多头={adl_long_count}, ADL空头={adl_short_count}")
        
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充出场信号

        基于ADL策略文档第3.3.3节的出场逻辑：
        - 主要依赖ROI和stoploss配置
        - 时间止损：防止资金被无效交易占用
        """
        # ADL策略主要依赖动态止盈止损，这里保持简单
        # 可以添加反向ADL信号作为出场确认

        # 反向信号出场（可选）
        dataframe.loc[dataframe['adl_short_signal'] == True, 'exit_long'] = 1
        dataframe.loc[dataframe['adl_long_signal'] == True, 'exit_short'] = 1

        return dataframe





    def confirm_trade_entry(self, pair: str, order_type: str, amount: float,
                           rate: float, time_in_force: str, current_time: datetime,
                           entry_tag: Optional[str], side: str, **kwargs) -> bool:
        """
        确认交易入场 - 🚀 多仓位优化版本

        ADL策略核心：强制使用挂单（Maker），利用USDC-M的0%费率
        新增：多仓位模式下的智能信号过滤和协调机制
        """
        # ADL策略要求：只交易USDC-M或USDT-M合约（利用0%挂单费率）
        if 'USDC' not in pair and 'USDT' not in pair:
            self.logger.error(f"❌ 拒绝非USDC/USDT合约 - {pair}: ADL策略要求0%挂单费率")
            return False

        if self.maker_only:
            # 检查是否为挂单
            if order_type != 'limit':
                self.logger.warning(f"❌ 拒绝市价单 - {pair}: ADL策略要求挂单执行")
                return False

        # 🚀 多仓位优化：智能信号过滤（仅在启用时执行）
        if self.multi_position_enabled:
            if not self._check_multi_position_entry(pair, current_time, entry_tag, side, **kwargs):
                return False

        contract_type = "USDC-M" if "USDC" in pair else "USDT-M"
        self.logger.info(f"✅ {contract_type}合约挂单确认 - {pair}")
        return True

    def _check_multi_position_entry(self, pair: str, current_time: datetime, 
                                   entry_tag: Optional[str], side: str, **kwargs) -> bool:
        """
        多仓位模式下的入场检查

        基于ADL策略核心思想的可解释性过滤：
        1. 信号唯一性：避免相似ADL信号重复开仓
        2. 时间间隔：确保ADL事件的自然间隔
        3. 仓位协调：确保多仓位有互补性
        4. 资金效率：优先分配给高质量信号
        """
        # 🚀 信号回收系统：获取当前信号信息
        current_signal_strength = 0
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if not dataframe.empty:
                current_signal_strength = dataframe.iloc[-1].get('adl_signal_strength', 0)
        except:
            pass

        # 获取该交易对的现有仓位
        existing_trades = []
        try:
            if hasattr(self.dp, 'get_trades'):
                all_trades = self.dp.get_trades()
                existing_trades = [trade for trade in all_trades 
                                 if trade.pair == pair and trade.is_open]
        except:
            # 如果无法获取交易数据，允许入场（向后兼容）
            pass

        # 1. 🔍 信号唯一性检查
        if existing_trades:
            # 获取当前信号强度
            try:
                dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
                if not dataframe.empty:
                    current_signal_strength = dataframe.iloc[-1].get('adl_signal_strength', 0)
                    
                    # 检查是否有显著更强的信号
                    max_existing_strength = 0
                    for trade in existing_trades:
                        existing_strength = getattr(trade, 'entry_signal_strength', 0)
                        max_existing_strength = max(max_existing_strength, existing_strength)
                    
                    # 要求新信号强度至少比现有信号强配置的倍数
                    required_strength = max_existing_strength * self.signal_upgrade_ratio
                    if current_signal_strength < required_strength:
                        self.logger.info(f"🔄 信号去重 - {pair}: 新信号强度{current_signal_strength:.2f} "
                                       f"< 现有最强信号{max_existing_strength:.2f} * {self.signal_upgrade_ratio}")
                        return False
                    else:
                        self.logger.info(f"✅ 信号升级 - {pair}: 新信号强度{current_signal_strength:.2f} "
                                       f"> 现有最强信号{max_existing_strength:.2f} * {self.signal_upgrade_ratio}")
            except:
                pass

        # 2. ⏰ 时间间隔检查（ADL事件的自然节奏） + 🚀 信号回收系统优化
        last_signal_time = self.last_signal_time.get(pair)
        if last_signal_time:
            time_diff_minutes = (current_time - last_signal_time).total_seconds() / 60
            
            # 根据信号质量确定最小间隔（使用配置参数）
            if entry_tag and 'tier1' in entry_tag:
                min_interval = self.tier1_min_interval  # Tier1信号间隔
            elif entry_tag and 'tier2' in entry_tag:
                min_interval = self.tier2_min_interval  # Tier2信号间隔
            else:
                min_interval = self.tier3_min_interval  # Tier3/其他信号间隔
            
            # 🚀 信号回收系统：动态时间调整
            if self.signal_recovery_enabled and self.dynamic_time_enabled:
                # 1. 超高质量信号可以缩短时间间隔
                if current_signal_strength >= self.priority_signal_strength:
                    min_interval *= self.high_strength_reduction  # 高强度信号减少60%等待时间
                    self.logger.info(f"🚀 高强度信号时间调整 - {pair}: 强度{current_signal_strength:.2f} >= {self.priority_signal_strength}, "
                                   f"间隔调整为{min_interval:.1f}分钟")
                
                # 2. 信号密度低时放宽时间限制
                self.signal_density_history.append(current_time)
                # 保留最近1小时的信号记录
                one_hour_ago = current_time - timedelta(hours=1)
                self.signal_density_history = [t for t in self.signal_density_history if t > one_hour_ago]
                
                recent_signal_count = len(self.signal_density_history)
                if recent_signal_count < self.signal_density_threshold:  # 信号密度低
                    min_interval *= self.low_density_relaxation  # 放宽时间限制
                    self.logger.info(f"🔄 低密度信号调整 - {pair}: 最近1小时{recent_signal_count}个信号 < {self.signal_density_threshold}, "
                                   f"间隔放宽为{min_interval:.1f}分钟")
            
            if time_diff_minutes < min_interval:
                # 🚀 信号回收系统：记录被拒绝的信号
                if self.signal_recovery_enabled and self.rejected_signal_tracking:
                    rejected_info = {
                        'pair': pair,
                        'time': current_time,
                        'reason': 'time_interval',
                        'signal_strength': current_signal_strength,
                        'entry_tag': entry_tag,
                        'required_interval': min_interval,
                        'actual_interval': time_diff_minutes
                    }
                    self.rejected_signals.append(rejected_info)
                    
                    # 保留最近100个被拒绝信号记录
                    if len(self.rejected_signals) > 100:
                        self.rejected_signals = self.rejected_signals[-100:]
                
                self.logger.info(f"⏰ 时间间隔不足 - {pair}: {time_diff_minutes:.1f}分钟 < {min_interval:.1f}分钟要求")
                return False

        # 3. 🎯 仓位协调检查（确保多样化而非重复）
        if existing_trades:
            # 检查方向分布
            same_direction_count = sum(1 for trade in existing_trades 
                                     if (side == 'long' and not trade.is_short) or 
                                        (side == 'short' and trade.is_short))
            
            # 限制同一方向的最大仓位数（使用配置参数）
            if same_direction_count >= self.max_same_direction:
                self.logger.info(f"🚫 方向集中度过高 - {pair}: {side}方向已有{same_direction_count}个仓位 "
                               f"(限制{self.max_same_direction}个)")
                return False
            
            # 检查entry_tag多样性（避免相同tier的过度集中）
            if entry_tag:
                same_tier_count = sum(1 for trade in existing_trades 
                                    if hasattr(trade, 'enter_tag') and 
                                       trade.enter_tag and entry_tag.split('_')[0] in trade.enter_tag)
                
                if same_tier_count >= self.max_same_tier:
                    self.logger.info(f"🚫 信号等级集中度过高 - {pair}: {entry_tag}类型已有{same_tier_count}个仓位 "
                                   f"(限制{self.max_same_tier}个)")
                    return False

        # 4. 💰 资金效率检查（确保有足够资金支持高质量信号）+ 🚀 仓位替换机制
        if len(existing_trades) >= self.max_total_positions:
            # 🚀 信号回收系统：仓位替换机制
            if (self.signal_recovery_enabled and self.position_replacement_enabled and 
                current_signal_strength >= self.priority_signal_strength):
                
                # 寻找可替换的低质量仓位
                weakest_trade = None
                weakest_strength = float('inf')
                
                for trade in existing_trades:
                    trade_strength = getattr(trade, 'entry_signal_strength', 0)
                    trade_duration = (current_time - trade.open_date_utc).total_seconds() / 60
                    
                    # 只考虑持续时间超过限制且质量低的仓位
                    if (trade_duration > self.replacement_time_limit and 
                        trade_strength < weakest_strength):
                        weakest_trade = trade
                        weakest_strength = trade_strength
                
                # 如果找到可替换的仓位且质量差距足够大
                if (weakest_trade and 
                    current_signal_strength - weakest_strength >= self.min_quality_gap):
                    
                    self.logger.info(f"🔄 仓位替换触发 - {pair}: 新信号强度{current_signal_strength:.2f} "
                                   f"替换弱信号{weakest_strength:.2f} (差距{current_signal_strength-weakest_strength:.2f}), "
                                   f"替换仓位已持续{(current_time - weakest_trade.open_date_utc).total_seconds()/60:.1f}分钟")
                    
                    # 这里可以添加强制平仓逻辑，但为了安全起见，我们记录并允许入场
                    # 实际的平仓会通过其他机制处理（如时间止损等）
                    return True
            
            if self.tier1_only_when_crowded and (not entry_tag or 'tier1' not in entry_tag):
                # 🚀 信号回收系统：记录被拒绝的信号
                if self.signal_recovery_enabled and self.rejected_signal_tracking:
                    rejected_info = {
                        'pair': pair,
                        'time': current_time,
                        'reason': 'position_limit',
                        'signal_strength': current_signal_strength,
                        'entry_tag': entry_tag,
                        'total_positions': len(existing_trades),
                        'max_positions': self.max_total_positions
                    }
                    self.rejected_signals.append(rejected_info)
                    
                    # 保留最近100个被拒绝信号记录
                    if len(self.rejected_signals) > 100:
                        self.rejected_signals = self.rejected_signals[-100:]
                
                self.logger.info(f"💰 资金效率优化 - {pair}: 已有{len(existing_trades)}个仓位 "
                               f"(限制{self.max_total_positions}个)，只接受Tier1信号")
                return False

        # 5. 📊 更新时间记录
        self.last_signal_time[pair] = current_time
        
        # 记录信号强度到trade对象（用于后续比较）
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if not dataframe.empty:
                current_signal_strength = dataframe.iloc[-1].get('adl_signal_strength', 0)
                # 这个值会在trade创建时通过kwargs传递
                kwargs = kwargs or {}
                kwargs['entry_signal_strength'] = current_signal_strength
        except:
            pass

        self.logger.info(f"✅ 多仓位检查通过 - {pair}: {entry_tag}, 现有仓位{len(existing_trades)}个")
        
        # 🚀 信号回收系统：定期报告被拒绝信号统计
        if (self.signal_recovery_enabled and self.rejected_signal_tracking and 
            len(self.rejected_signals) > 0 and len(self.rejected_signals) % 20 == 0):
            self._report_rejected_signals_stats()
        
        return True

    def _report_rejected_signals_stats(self):
        """报告被拒绝信号的统计信息"""
        if not self.rejected_signals:
            return
        
        # 按拒绝原因分组统计
        reason_stats = {}
        strength_stats = []
        
        for signal in self.rejected_signals[-50:]:  # 分析最近50个被拒绝信号
            reason = signal.get('reason', 'unknown')
            strength = signal.get('signal_strength', 0)
            
            reason_stats[reason] = reason_stats.get(reason, 0) + 1
            strength_stats.append(strength)
        
        # 计算强度统计
        if strength_stats:
            avg_strength = sum(strength_stats) / len(strength_stats)
            max_strength = max(strength_stats)
            high_quality_count = sum(1 for s in strength_stats if s >= self.high_quality_signal_threshold)
        else:
            avg_strength = max_strength = high_quality_count = 0
        
        self.logger.info("📊 === 被拒绝信号统计报告 ===")
        self.logger.info(f"📊 总被拒绝信号: {len(self.rejected_signals)}")
        self.logger.info(f"📊 拒绝原因统计: {reason_stats}")
        self.logger.info(f"📊 平均信号强度: {avg_strength:.2f}")
        self.logger.info(f"📊 最强信号强度: {max_strength:.2f}")
        self.logger.info(f"📊 高质量信号(>={self.high_quality_signal_threshold}): {high_quality_count}")
        self.logger.info("📊 === 统计报告结束 ===")

    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs) -> Optional[Union[str, bool]]:
        """
        自定义出场逻辑 - 🚀 第三步优化：激进时间管理 + 盈利保护

        新增功能：
        - 根据信号等级设置不同的时间限制
        - 盈利保护：有浮盈后立即切换到更严格的时间控制
        - 快速亏损止损：早期出现较大亏损时提前退出
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return None

        # 获取入场时的ATR值（与止损逻辑一致）
        entry_atr = getattr(trade, 'entry_atr', None)
        if entry_atr is None:
            entry_atr = dataframe.iloc[-1].get('atr', 0)
            trade.entry_atr = entry_atr

        if entry_atr == 0:
            return None

        # ATR动态止盈检查（保持原有逻辑）
        atr_profit_multiplier = self.atr_profit_multiplier

        # 计算动态止盈目标
        if trade.is_short == False:  # 多头交易
            target_profit_price = trade.open_rate + (atr_profit_multiplier * entry_atr)
            target_profit_pct = (target_profit_price - trade.open_rate) / trade.open_rate
            if current_profit >= target_profit_pct:
                self.logger.info(f"✅ ATR动态止盈触发 - {pair}: 入场ATR={entry_atr:.6f}, 倍数{atr_profit_multiplier}, 目标{target_profit_pct:.3f}%, 当前{current_profit:.3f}%")
                return "atr_profit_target"
        else:  # 空头交易
            target_profit_price = trade.open_rate - (atr_profit_multiplier * entry_atr)
            target_profit_pct = (trade.open_rate - target_profit_price) / trade.open_rate
            if current_profit >= target_profit_pct:
                self.logger.info(f"✅ ATR动态止盈触发 - {pair}: 入场ATR={entry_atr:.6f}, 倍数{atr_profit_multiplier}, 目标{target_profit_pct:.3f}%, 当前{current_profit:.3f}%")
                return "atr_profit_target"

        # 🚀 第三步优化：激进时间管理
        trade_duration = current_time - trade.open_date_utc
        
        if self.step3_enabled and self.aggressive_time_enabled:
            # 🛡️ 回滚到baseline：调整快速亏损止损条件
            if (self.profit_protection_enabled and 
                current_profit < self.quick_loss_threshold and 
                trade_duration.total_seconds() > 480):  # 8分钟后检查
                self.logger.info(f"❌ 快速亏损止损触发 - {pair}: 亏损{current_profit:.3f}% > 阈值{self.quick_loss_threshold:.3f}%")
                return "quick_loss_exit"
            
            # 🎯 根据信号等级确定时间限制
            entry_tag = getattr(trade, 'enter_tag', 'unknown')
            
            # 确定信号等级
            if 'tier1' in entry_tag:
                time_limits = self.tier1_time_limits
                tier = "T1"
            elif 'tier2' in entry_tag:
                time_limits = self.tier2_time_limits  
                tier = "T2"
            elif 'tier3' in entry_tag:
                time_limits = self.tier3_time_limits
                tier = "T3"
            else:
                # 未知等级，使用tier2作为默认值
                time_limits = self.tier2_time_limits
                tier = "T2(默认)"
            
            # 🛡️ 第四步优化：调整盈利保护阈值
            if (self.profit_protection_enabled and 
                current_profit > self.profit_protection_threshold):
                time_limit_minutes = 15  # 盈利保护时间限制
                protection_status = "盈利保护"
            else:
                # 根据ATR动态调整时间限制（保持原有逻辑但使用新的时间范围）
                current_atr = dataframe.iloc[-1].get('atr', 0)
                atr_sma = dataframe.iloc[-1].get('atr_sma', 0)
                
                if atr_sma > 0:
                    atr_ratio = current_atr / atr_sma
                else:
                    atr_ratio = self.atr_default_ratio
                
                # 根据ATR比率选择时间限制
                if atr_ratio >= self.atr_very_high_threshold:
                    time_limit_minutes = time_limits[4]  # 最长时间
                    volatility_level = "极高波动"
                elif atr_ratio >= self.atr_high_threshold:
                    time_limit_minutes = time_limits[3]
                    volatility_level = "高波动"
                elif atr_ratio >= self.atr_medium_threshold:
                    time_limit_minutes = time_limits[2]  # 中间值
                    volatility_level = "中波动"
                elif atr_ratio >= self.atr_low_threshold:
                    time_limit_minutes = time_limits[1]
                    volatility_level = "低波动"
                else:
                    time_limit_minutes = time_limits[0]  # 最短时间
                    volatility_level = "极低波动"
                
                protection_status = f"{volatility_level}(ATR比率{atr_ratio:.2f})"
            

            
            # 检查是否超过时间限制
            time_limit_seconds = time_limit_minutes * 60
            
            if trade_duration.total_seconds() > time_limit_seconds:
                self.logger.info(f"⏰ 激进时间止损触发 - {pair}: 持仓{trade_duration}, "
                               f"{tier}等级限制{time_limit_minutes}分钟, {protection_status}, "
                               f"当前盈利{current_profit:.3f}%")
                return "aggressive_time_stop"
        
        else:
            # 第三步优化未启用，使用原有的动态时间止损逻辑
            current_atr = dataframe.iloc[-1].get('atr', 0)
            atr_sma = dataframe.iloc[-1].get('atr_sma', 0)

            if atr_sma > 0:
                atr_ratio = current_atr / atr_sma
            else:
                atr_ratio = self.atr_default_ratio

            # 原有时间限制逻辑
            if atr_ratio >= self.atr_very_high_threshold:
                time_limit_minutes = 25
                volatility_level = "极高波动"
            elif atr_ratio >= self.atr_high_threshold:
                time_limit_minutes = 22
                volatility_level = "高波动"
            elif atr_ratio >= self.atr_medium_threshold:
                time_limit_minutes = 18
                volatility_level = "中波动"
            elif atr_ratio >= self.atr_low_threshold:
                time_limit_minutes = 15
                volatility_level = "低波动"
            else:
                time_limit_minutes = 12
                volatility_level = "极低波动"

            time_limit_seconds = time_limit_minutes * 60

            if trade_duration.total_seconds() > time_limit_seconds:
                self.logger.info(f"⏰ 动态时间止损触发 - {pair}: 持仓{trade_duration}, "
                               f"ATR比率{atr_ratio:.2f}({volatility_level}), 限制{time_limit_minutes}分钟")
                return "time_stop"

        # 🚀 第一步重构（第三版）：多重止盈系统 - 严格从配置读取
        multi_exit_config = self.config['adl_strategy_params']['multi_exit_system']
        
        if multi_exit_config['enable']:
            # 根据入场标签确定止盈目标
            entry_tag = getattr(trade, 'enter_tag', 'unknown')
            
            if 'tier1' in entry_tag:
                profit_targets = multi_exit_config['tier1_profit_targets']
                tier_level = "T1"
            elif 'tier2' in entry_tag:
                profit_targets = multi_exit_config['tier2_profit_targets']
                tier_level = "T2"
            elif 'tier3' in entry_tag:
                profit_targets = multi_exit_config['tier3_profit_targets']
                tier_level = "T3"
            else:
                profit_targets = multi_exit_config['tier1_profit_targets']  # 使用tier1作为默认
                tier_level = "默认"
            
            # 波动率自适应调整
            if multi_exit_config['volatility_adaptive']:
                # 获取当前ATR相对强度
                try:
                    dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
                    if not dataframe.empty:
                        current_atr = dataframe.iloc[-1].get('atr', 0)
                        atr_sma = dataframe.iloc[-1].get('atr_sma', current_atr)
                        
                        if current_atr > 0 and atr_sma > 0:
                            volatility_ratio = current_atr / atr_sma
                            
                            # 高波动率提高止盈目标，低波动率降低目标
                            if volatility_ratio > self.vol_high_threshold:  # 高波动
                                multiplier = self.vol_high_multiplier
                                volatility_status = "高波动"
                            elif volatility_ratio > self.vol_medium_threshold:  # 中等波动 
                                multiplier = self.vol_medium_multiplier
                                volatility_status = "中等波动"
                            else:  # 低波动
                                multiplier = self.vol_low_multiplier
                                volatility_status = "低波动"
                            
                            profit_targets = [target * multiplier for target in profit_targets]
                except:
                    volatility_status = "未知波动"
                    pass
            
            # 检查多重止盈触发
            for i, target in enumerate(profit_targets):
                if current_profit >= target:
                    # 检查是否已经执行过此级别的止盈
                    exit_tag = f"multi_exit_L{i+1}_{tier_level}"
                    
                    self.logger.info(f"🎯 多重止盈触发 - {pair}: 级别L{i+1}/{len(profit_targets)}, "
                                   f"目标{target:.1%}, 当前{current_profit:.3%}, "
                                   f"{tier_level}信号, {volatility_status}")
                    
                    return exit_tag
            
            # 信号强度追踪 - 如果信号正在衰减，提前止盈
            if multi_exit_config['signal_maturity_tracking']:
                # 持仓时间超过10分钟且有小幅盈利，检查信号强度变化
                if (trade_duration.total_seconds() > 600 and  # 10分钟
                    current_profit > self.min_profit_for_signal_tracking):  # 从配置读取盈利阈值
                    
                    try:
                        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
                        if not dataframe.empty:
                            current_signal_strength = dataframe.iloc[-1].get('adl_signal_strength', 0)
                            
                            # 如果当前信号强度明显下降，执行成熟度止盈
                            if current_signal_strength < self.signal_strength_decline_threshold:  # 信号强度下降
                                self.logger.info(f"📈 信号成熟度止盈 - {pair}: 信号强度下降至{current_signal_strength:.2f}, "
                                               f"当前盈利{current_profit:.3%}")
                                return "signal_maturity_exit"
                    except:
                        pass

        return None



    def adjust_trade_position(self, trade: Trade, current_time: datetime,
                             current_rate: float, current_profit: float,
                             min_stake: Optional[float], max_stake: float,
                             current_entry_rate: float, current_exit_rate: float,
                             current_entry_profit: float, current_exit_profit: float,
                             **kwargs) -> Optional[float]:
        """
        基于ADL信号强度的动态加仓策略
        
        核心逻辑：
        1. 只有在盈利或微亏时才考虑加仓
        2. 基于当前ADL信号强度决定加仓规模
        3. 根据tier等级设置不同的加仓策略
        4. 最多允许4次加仓（配置中的max_entry_position_adjustment）
        """
        try:
            # 获取当前数据
            dataframe, _ = self.dp.get_analyzed_dataframe(trade.pair, self.timeframe)
            if dataframe.empty:
                return None
            
            # 只有在盈利或微亏时才考虑加仓（更保守的条件）
            if current_profit < self.max_loss_for_position_adjustment:  # 从配置读取亏损阈值
                self.logger.debug(f"⛔ 加仓取消 - {trade.pair}: 亏损过大 {current_profit:.3%}")
                return None
            
            # 获取当前ADL信号强度
            current_signal_strength = dataframe.iloc[-1].get('adl_signal_strength', 0)
            current_adl_long_signal = dataframe.iloc[-1].get('adl_long_signal', False)
            current_adl_short_signal = dataframe.iloc[-1].get('adl_short_signal', False)
            
            # 检查是否有当前ADL信号（同向）
            has_same_direction_signal = (
                (not trade.is_short and current_adl_long_signal) or
                (trade.is_short and current_adl_short_signal)
            )
            
            if not has_same_direction_signal:
                self.logger.debug(f"⛔ 加仓取消 - {trade.pair}: 无同向ADL信号")
                return None
            
            # 根据入场标签确定加仓策略
            entry_tag = getattr(trade, 'enter_tag', 'unknown')
            
            if 'tier1' in entry_tag:
                # Tier1信号：激进加仓策略
                min_signal_strength = self.tier1_min_signal_strength
                max_additions = self.tier1_max_additions
                position_multiplier = self.tier1_position_multiplier
                tier_level = "T1"
            elif 'tier2' in entry_tag:
                # Tier2信号：中等加仓策略
                min_signal_strength = self.tier2_min_signal_strength
                max_additions = self.tier2_max_additions
                position_multiplier = self.tier2_position_multiplier
                tier_level = "T2"
            elif 'tier3' in entry_tag:
                # Tier3信号：保守加仓策略
                min_signal_strength = self.tier3_min_signal_strength
                max_additions = self.tier3_max_additions
                position_multiplier = self.tier3_position_multiplier
                tier_level = "T3"
            else:
                # 未知tier，使用默认策略
                min_signal_strength = self.default_min_signal_strength
                max_additions = self.default_max_additions
                position_multiplier = self.default_position_multiplier
                tier_level = "默认"
            
            # 检查信号强度是否足够
            if current_signal_strength < min_signal_strength:
                self.logger.debug(f"⛔ 加仓取消 - {trade.pair}: 信号强度不足 {current_signal_strength:.2f} < {min_signal_strength}")
                return None
            
            # 检查已加仓次数
            filled_entries = trade.filled_entries if hasattr(trade, 'filled_entries') else []
            current_additions = len(filled_entries) - 1  # 减去初始入场
            
            if current_additions >= max_additions:
                self.logger.debug(f"⛔ 加仓取消 - {trade.pair}: 已达最大加仓次数 {current_additions}/{max_additions}")
                return None
            
            # 计算加仓规模
            initial_stake = trade.stake_amount
            additional_stake = initial_stake * position_multiplier
            
            # 确保不超过最大仓位限制
            additional_stake = min(additional_stake, max_stake)
            additional_stake = max(additional_stake, min_stake) if min_stake else additional_stake
            
            # 额外的安全检查：基于时间间隔
            trade_duration = current_time - trade.open_date_utc
            
            if trade_duration.total_seconds() < self.pos_adj_min_interval_minutes * 60:
                self.logger.debug(f"⛔ 加仓取消 - {trade.pair}: 时间间隔不足 {trade_duration}")
                return None
            
            # 基于波动率调整加仓规模
            try:
                current_atr = dataframe.iloc[-1].get('atr', 0)
                atr_sma = dataframe.iloc[-1].get('atr_sma', current_atr)
                
                if current_atr > 0 and atr_sma > 0:
                    volatility_ratio = current_atr / atr_sma
                    
                    # 高波动时增加加仓规模，低波动时减少
                    if volatility_ratio > self.pos_vol_high_threshold:  # 高波动
                        vol_multiplier = self.pos_vol_high_multiplier
                        vol_status = "高波动"
                    elif volatility_ratio > self.pos_vol_medium_threshold:  # 中等波动
                        vol_multiplier = self.pos_vol_medium_multiplier
                        vol_status = "中等波动"
                    else:  # 低波动
                        vol_multiplier = self.pos_vol_low_multiplier
                        vol_status = "低波动"
                    
                    additional_stake *= vol_multiplier
                else:
                    vol_status = "未知波动"
            except:
                vol_status = "波动率计算失败"
            
            stake_currency = "USDT" if "USDT" in trade.pair else "USDC"
            self.logger.info(f"💰 执行加仓 - {trade.pair}: {tier_level}信号(强度{current_signal_strength:.2f}), "
                           f"第{current_additions + 1}次加仓, 规模{additional_stake:.2f} {stake_currency}, "
                           f"当前盈利{current_profit:.3%}, {vol_status}")
            
            return additional_stake
            
        except Exception as e:
            self.logger.error(f"❌ 加仓策略执行失败 - {trade.pair}: {e}")
            return None

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: Optional[str],
                 side: str, **kwargs) -> float:
        """
        动态杠杆控制方法
        
        根据信号等级动态调整杠杆：
        - Tier1信号：使用较高杠杆（提升收益）
        - Tier2信号：使用中等杠杆  
        - Tier3信号：使用较低杠杆（控制风险）
        """
        if not self.step3_enabled:
            # 第三步优化未启用时，使用固定杠杆
            return min(self.fallback_leverage_step3_disabled, max_leverage)
        
        # 根据信号等级确定杠杆（使用配置文件参数）
        if entry_tag:
            if 'tier1' in entry_tag:
                # Tier1信号：使用配置文件中的tier1_leverage
                leverage = min(self.tier1_leverage, max_leverage)
                self.logger.info(f"🎯 Tier1信号杠杆 - {pair}: {leverage:.1f}x (配置值: {self.tier1_leverage}x)")
            elif 'tier2' in entry_tag:
                # Tier2信号：使用配置文件中的tier2_leverage
                leverage = min(self.tier2_leverage, max_leverage)
                self.logger.info(f"🎯 Tier2信号杠杆 - {pair}: {leverage:.1f}x (配置值: {self.tier2_leverage}x)")
            elif 'tier3' in entry_tag:
                # Tier3信号：使用配置文件中的tier3_leverage
                leverage = min(self.tier3_leverage, max_leverage)
                self.logger.info(f"🎯 Tier3信号杠杆 - {pair}: {leverage:.1f}x (配置值: {self.tier3_leverage}x)")
            else:
                # 未知等级，使用base_leverage
                leverage = min(self.base_leverage, max_leverage)
                self.logger.info(f"🎯 默认信号杠杆 - {pair}: {leverage:.1f}x (base_leverage: {self.base_leverage}x)")
        else:
            # 无entry_tag时使用默认杠杆
            leverage = min(self.fallback_leverage_no_tag, max_leverage)
            self.logger.info(f"🎯 默认杠杆 - {pair}: {leverage:.1f}x (无标签)")
        
        return leverage

    def adjust_entry_price(self, trade: Trade, order: Order, pair: str,
                          current_time: datetime, proposed_rate: float, current_order_rate: float,
                          entry_tag: Optional[str], side: str, **kwargs) -> float:
        """
        调整加仓入场价格
        
        策略：使用稍微更有利的价格来增加成交概率
        """
        try:
            # 获取当前市场数据
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if dataframe.empty:
                return proposed_rate
            
            current_atr = dataframe.iloc[-1].get('atr', 0)
            if current_atr == 0:
                return proposed_rate
            
            # 使用ATR的配置比例作为价格调整幅度
            price_adjustment = current_atr * self.atr_adjustment_ratio
            
            if side == 'long':
                # 多头加仓：使用稍低价格增加成交概率
                adjusted_price = proposed_rate - price_adjustment
                self.logger.debug(f"📈 多头加仓价格调整 - {pair}: {proposed_rate:.6f} -> {adjusted_price:.6f}")
                return max(adjusted_price, proposed_rate * self.max_adjustment_long)  # 从配置读取最大调整比例
            else:
                # 空头加仓：使用稍高价格增加成交概率
                adjusted_price = proposed_rate + price_adjustment
                self.logger.debug(f"📉 空头加仓价格调整 - {pair}: {proposed_rate:.6f} -> {adjusted_price:.6f}")
                return min(adjusted_price, proposed_rate * self.max_adjustment_short)  # 从配置读取最大调整比例
                
        except Exception as e:
            self.logger.warning(f"⚠️ 加仓价格调整失败 - {pair}: {e}")
            return proposed_rate


