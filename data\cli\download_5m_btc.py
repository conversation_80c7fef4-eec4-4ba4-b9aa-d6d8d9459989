#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
专用5分钟BTC_USDT数据下载脚本

直接下载原生5分钟BTC_USDT历史数据，用于5分钟SMC策略回测
避免使用1分钟数据重采样，提供更准确的OHLCV数据
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from data.sources.ccxt_source import CCXTDataSource, CCXTConfig
from data.storage.optimized_storage import OptimizedStorage


def download_5m_btc_data(days: int = 30, output_dir: str = None) -> bool:
    """
    下载原生5分钟BTC_USDT历史数据
    
    Args:
        days: 要下载的天数（默认30天）
        output_dir: 输出目录（默认使用项目数据目录）
        
    Returns:
        下载成功返回True，失败返回False
    """
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger('5m-btc-download')
    
    # 设置输出目录
    if output_dir is None:
        output_dir = project_root / "data" / "storage" / "data"
    
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 计算时间范围
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)
    
    logger.info(f"🚀 开始下载5分钟BTC_USDT原生数据")
    logger.info(f"📅 时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} 到 {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"📁 输出目录: {output_dir}")
    
    try:
        # 创建CCXT配置（使用Binance，无需API密钥获取公开数据）
        config = CCXTConfig(
            exchange_id='binance',
            enable_rate_limit=True,
            timeout=10000  # 10秒超时
        )
        
        # 初始化数据源
        logger.info("🔗 连接到Binance交易所...")
        source = CCXTDataSource(config)
        
        # 初始化存储（启用压缩）
        storage = OptimizedStorage(str(output_dir), compression=True)
        
        # 下载5分钟数据
        symbol = 'BTC/USDT'
        timeframe = '5m'
        
        logger.info(f"📊 开始下载 {symbol} {timeframe} 数据...")
        data = source.get_data(symbol, timeframe, start_time, end_time)
        
        if data.empty:
            logger.error("❌ 未获取到任何数据")
            return False
        
        # 数据质量检查
        logger.info(f"✅ 获取了 {len(data)} 条5分钟数据")
        logger.info(f"📈 数据范围: {data.index[0]} 到 {data.index[-1]}")
        
        # 检查数据完整性
        expected_points = int((end_time - start_time).total_seconds() / 300)  # 5分钟 = 300秒
        completeness = len(data) / expected_points * 100
        logger.info(f"📊 数据完整性: {completeness:.1f}% ({len(data)}/{expected_points})")
        
        # 检查数据质量
        missing_values = data.isnull().sum().sum()
        if missing_values > 0:
            logger.warning(f"⚠️ 发现 {missing_values} 个缺失值")
        
        # 检查OHLCV逻辑
        invalid_ohlc = ((data['high'] < data['low']) | 
                       (data['high'] < data['open']) | 
                       (data['high'] < data['close']) |
                       (data['low'] > data['open']) |
                       (data['low'] > data['close'])).sum()
        
        if invalid_ohlc > 0:
            logger.warning(f"⚠️ 发现 {invalid_ohlc} 条OHLC逻辑异常的数据")
        
        # 保存数据
        logger.info("💾 保存5分钟数据...")
        storage.save_data(data, 'BTC_USDT', '5m')
        
        # 验证保存结果
        if storage.has_data('BTC_USDT', '5m'):
            info = storage.get_data_info('BTC_USDT', '5m')
            logger.info(f"✅ 数据保存成功!")
            logger.info(f"📁 文件路径: {info.get('file_path', 'N/A')}")
            logger.info(f"📊 保存记录数: {info.get('rows', 0)}")
            logger.info(f"💾 文件大小: {info.get('file_size', 0) / 1024 / 1024:.2f} MB")
            logger.info(f"🗜️ 压缩格式: {'是' if info.get('compressed', False) else '否'}")
            
            return True
        else:
            logger.error("❌ 数据保存验证失败")
            return False
    
    except Exception as e:
        logger.error(f"❌ 下载失败: {str(e)}")
        import traceback
        logger.error(f"详细错误:\n{traceback.format_exc()}")
        return False


def verify_5m_data(data_dir: str = None) -> bool:
    """
    验证5分钟数据的质量和完整性
    
    Args:
        data_dir: 数据目录
        
    Returns:
        验证通过返回True
    """
    if data_dir is None:
        data_dir = project_root / "data" / "storage" / "data"
    
    logger = logging.getLogger('5m-data-verify')
    
    try:
        storage = OptimizedStorage(str(data_dir), compression=True)
        
        if not storage.has_data('BTC_USDT', '5m'):
            logger.error("❌ 5分钟BTC_USDT数据不存在")
            return False
        
        # 加载数据进行验证
        data = storage.load_data('BTC_USDT', '5m')
        
        if data.empty:
            logger.error("❌ 5分钟数据为空")
            return False
        
        logger.info(f"📊 5分钟数据验证:")
        logger.info(f"   数据条数: {len(data)}")
        logger.info(f"   时间范围: {data.index[0]} 到 {data.index[-1]}")
        logger.info(f"   数据列: {list(data.columns)}")
        
        # 检查必要的列
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            logger.error(f"❌ 缺少必要的列: {missing_columns}")
            return False
        
        # 检查数据类型
        for col in required_columns:
            if not data[col].dtype.kind in 'biufc':  # 数值类型
                logger.error(f"❌ 列 {col} 不是数值类型: {data[col].dtype}")
                return False
        
        # 检查价格范围合理性
        price_cols = ['open', 'high', 'low', 'close']
        for col in price_cols:
            if data[col].min() <= 0:
                logger.error(f"❌ 发现非正价格在列 {col}")
                return False
            
            if data[col].max() > 1000000:  # BTC价格不太可能超过100万
                logger.warning(f"⚠️ 发现异常高价格在列 {col}: {data[col].max()}")
        
        # 检查成交量
        if (data['volume'] < 0).any():
            logger.error("❌ 发现负成交量")
            return False
        
        logger.info("✅ 5分钟数据验证通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据验证失败: {str(e)}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='下载5分钟BTC_USDT原生数据')
    parser.add_argument('--days', '-d', type=int, default=30, help='下载天数（默认30天）')
    parser.add_argument('--output', '-o', help='输出目录')
    parser.add_argument('--verify-only', action='store_true', help='仅验证现有数据')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细日志')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    if args.verify_only:
        # 仅验证数据
        success = verify_5m_data(args.output)
    else:
        # 下载数据
        success = download_5m_btc_data(args.days, args.output)
        
        # 下载成功后验证
        if success:
            success = verify_5m_data(args.output)
    
    return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())
