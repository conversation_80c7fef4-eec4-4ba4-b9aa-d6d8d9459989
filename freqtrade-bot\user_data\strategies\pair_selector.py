"""
ADL策略动态交易对筛选器
功能：基于历史表现自动筛选最优交易对，提升策略整体收益
作者：ADL策略优化系统
版本：1.0
"""

import pandas as pd
import numpy as np
import json
import logging
from typing import List, Dict, Tu<PERSON>
from datetime import datetime, timedelta
import ccxt
from pathlib import Path
import subprocess
import os

class ADLPairSelector:
    """ADL策略动态交易对筛选器"""
    
    def __init__(self, config_path: str = "config_adl_backtest.json"):
        """
        初始化交易对筛选器
        
        Args:
            config_path: FreqTrade配置文件路径
        """
        self.config_path = config_path
        self.logger = logging.getLogger(__name__)
        
        # 筛选标准权重
        self.weights = {
            'win_rate': 0.40,      # 胜率权重40%
            'profit_factor': 0.30,  # 利润因子权重30%
            'atr_success_rate': 0.20, # ATR止盈成功率权重20%
            'liquidity': 0.10       # 流动性权重10%
        }
        
        # 筛选阈值
        self.thresholds = {
            'min_win_rate': 0.60,           # 最低胜率60%
            'min_profit_factor': 0.80,      # 最低利润因子0.8
            'min_atr_success_rate': 0.40,   # 最低ATR止盈成功率40%
            'max_single_loss': 0.02,        # 最大单笔亏损2%
            'max_avg_duration': 25,         # 最大平均交易时长25分钟
            'min_volume_usdt': 10_000_000    # 最低24小时交易量1000万USDT
        }
        
        # 板块分类（确保风险分散）
        self.sectors = {
            'BTC_ECOSYSTEM': ['BTC'],
            'ETH_ECOSYSTEM': ['ETH'],
            'LAYER1': ['SOL', 'ADA', 'AVAX', 'DOT', 'ATOM', 'NEAR', 'ALGO'],
            'DEFI': ['UNI', 'AAVE', 'COMP', 'MKR', 'SNX', 'CRV', 'SUSHI'],
            'EXCHANGE': ['BNB', 'FTT', 'OKB', 'HT'],
            'MEME': ['DOGE', 'SHIB', 'PEPE'],
            'GAMING': ['AXS', 'SAND', 'MANA', 'ENJ'],
            'INFRASTRUCTURE': ['LINK', 'GRT', 'FIL', 'AR']
        }

    def get_adl_suitable_pairs(self) -> List[str]:
        """
        获取适合ADL策略的交易对候选池
        基于以下特征筛选：
        1. 高流动性（支持大额ADL事件）
        2. 高波动性（ADL事件后价格反弹空间）
        3. 活跃交易（频繁的ADL事件）
        4. 主流币种（数据质量可靠）

        Returns:
            适合ADL策略的交易对列表（约50个）
        """
        # ADL策略适合的交易对候选池
        # 按市值和流动性分层选择
        adl_suitable_pairs = [
            # 第一层：超大市值币种（BTC生态）
            'BTC/USDC:USDC',

            # 第二层：大市值币种（ETH生态）
            'ETH/USDC:USDC',

            # 第三层：主流Layer1（高波动性，频繁ADL）
            'SOL/USDC:USDC', 'ADA/USDC:USDC', 'AVAX/USDC:USDC', 'DOT/USDC:USDC',
            'ATOM/USDC:USDC', 'NEAR/USDC:USDC', 'ALGO/USDC:USDC', 'FTM/USDC:USDC',
            'MATIC/USDC:USDC', 'ONE/USDC:USDC', 'HBAR/USDC:USDC',

            # 第四层：交易所代币（高流动性）
            'BNB/USDC:USDC', 'OKB/USDC:USDC', 'FTT/USDC:USDC',

            # 第五层：DeFi蓝筹（波动性适中，ADL频率稳定）
            'UNI/USDC:USDC', 'AAVE/USDC:USDC', 'COMP/USDC:USDC', 'MKR/USDC:USDC',
            'SNX/USDC:USDC', 'CRV/USDC:USDC', 'SUSHI/USDC:USDC', '1INCH/USDC:USDC',

            # 第六层：基础设施代币（稳定的ADL模式）
            'LINK/USDC:USDC', 'GRT/USDC:USDC', 'FIL/USDC:USDC', 'AR/USDC:USDC',
            'STORJ/USDC:USDC',

            # 第七层：热门Meme币（高波动性，频繁ADL）
            'DOGE/USDC:USDC', 'SHIB/USDC:USDC', 'PEPE/USDC:USDC', 'FLOKI/USDC:USDC',

            # 第八层：游戏/元宇宙（周期性ADL模式）
            'AXS/USDC:USDC', 'SAND/USDC:USDC', 'MANA/USDC:USDC', 'ENJ/USDC:USDC',
            'GALA/USDC:USDC',

            # 第九层：新兴Layer1/Layer2
            'APT/USDC:USDC', 'SUI/USDC:USDC', 'ARB/USDC:USDC', 'OP/USDC:USDC',
            'LDO/USDC:USDC', 'IMX/USDC:USDC',

            # 第十层：其他高流动性币种
            'LTC/USDC:USDC', 'BCH/USDC:USDC', 'ETC/USDC:USDC', 'XRP/USDC:USDC',
            'TRX/USDC:USDC', 'XLM/USDC:USDC', 'VET/USDC:USDC', 'THETA/USDC:USDC'
        ]

        self.logger.info(f"ADL策略候选交易对池: {len(adl_suitable_pairs)} 个")
        return adl_suitable_pairs

    def get_binance_usdc_pairs(self) -> List[str]:
        """
        获取Binance所有USDC-M永续合约交易对（备用方法）

        Returns:
            USDC-M交易对列表
        """
        try:
            exchange = ccxt.binance({
                'sandbox': False,
                'enableRateLimit': True,
                'options': {'defaultType': 'future'}
            })

            markets = exchange.load_markets()
            usdc_pairs = []

            for symbol, market in markets.items():
                if (market['quote'] == 'USDC' and
                    market['type'] == 'future' and
                    market['active'] and
                    ':USDC' in symbol):
                    usdc_pairs.append(symbol)

            self.logger.info(f"从Binance API获取 {len(usdc_pairs)} 个USDC-M交易对")
            return sorted(usdc_pairs)

        except Exception as e:
            self.logger.error(f"获取Binance交易对失败: {e}")
            # 返回ADL适合的候选池
            return self.get_adl_suitable_pairs()

    def get_pair_liquidity(self, pair: str) -> float:
        """
        获取交易对24小时交易量
        
        Args:
            pair: 交易对符号
            
        Returns:
            24小时交易量(USDT)
        """
        try:
            exchange = ccxt.binance({
                'sandbox': False,
                'enableRateLimit': True,
                'options': {'defaultType': 'future'}
            })
            
            ticker = exchange.fetch_ticker(pair)
            volume_usdt = ticker.get('quoteVolume', 0)
            
            return float(volume_usdt) if volume_usdt else 0
            
        except Exception as e:
            self.logger.warning(f"获取 {pair} 流动性失败: {e}")
            return 0

    def run_pair_backtest(self, pair: str, timerange: str = "20250520-20250619") -> Dict:
        """
        对单个交易对运行ADL策略回测
        
        Args:
            pair: 交易对符号
            timerange: 回测时间范围
            
        Returns:
            回测结果字典
        """
        try:
            # 创建临时配置文件（确保在正确的目录）
            temp_config_name = f"temp_config_{pair.replace('/', '_').replace(':', '_')}.json"

            # 确定配置文件的完整路径
            if 'freqtrade-bot' in self.config_path:
                # 如果config_path已包含freqtrade-bot路径
                config_dir = os.path.dirname(self.config_path)
                temp_config = os.path.join(config_dir, temp_config_name)
            else:
                # 如果config_path是相对路径，添加freqtrade-bot前缀
                config_dir = 'freqtrade-bot'
                temp_config = os.path.join(config_dir, temp_config_name)

            # 读取原配置
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 修改交易对
            config['exchange']['pair_whitelist'] = [pair]

            # 确保目录存在
            os.makedirs(os.path.dirname(temp_config), exist_ok=True)

            # 保存临时配置
            with open(temp_config, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)
            
            # 运行回测
            cmd = [
                'python', '-m', 'freqtrade', 'backtesting',
                '--config', temp_config_name,
                '--strategy', 'ADLAnticipationStrategy',
                '--timerange', timerange,
                '--timeframe', '5m',
                '--cache', 'none',
                '--dry-run-wallet', '10000',
                '--export', 'trades'
            ]

            # 设置工作目录为freqtrade-bot
            cwd = os.path.join(os.getcwd(), 'freqtrade-bot') if 'freqtrade-bot' not in os.getcwd() else os.getcwd()
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=cwd)
            
            # 清理临时文件
            if os.path.exists(temp_config):
                os.remove(temp_config)
            
            if result.returncode == 0:
                return self._parse_backtest_output(result.stdout, pair)
            else:
                self.logger.error(f"{pair} 回测失败: {result.stderr}")
                return self._get_default_metrics(pair)
                
        except Exception as e:
            self.logger.error(f"{pair} 回测异常: {e}")
            return self._get_default_metrics(pair)

    def _parse_backtest_output(self, output: str, pair: str) -> Dict:
        """
        解析回测输出结果
        
        Args:
            output: 回测输出文本
            pair: 交易对符号
            
        Returns:
            解析后的指标字典
        """
        metrics = self._get_default_metrics(pair)
        
        try:
            lines = output.split('\n')
            
            for line in lines:
                # 解析总体指标
                if 'Total/Daily Avg Trades' in line:
                    trades = line.split('|')[2].strip().split('/')[0].strip()
                    metrics['total_trades'] = int(trades) if trades.isdigit() else 0
                
                elif 'Profit factor' in line:
                    pf = line.split('|')[2].strip()
                    try:
                        metrics['profit_factor'] = float(pf)
                    except:
                        pass
                
                elif 'Win%' in line and pair in line:
                    parts = line.split('|')
                    if len(parts) >= 7:
                        win_rate_str = parts[-1].strip()
                        try:
                            metrics['win_rate'] = float(win_rate_str) / 100.0
                        except:
                            pass
                
                # 解析退出原因统计
                elif 'atr_profit_target' in line:
                    parts = line.split('|')
                    if len(parts) >= 3:
                        atr_exits = parts[2].strip()
                        try:
                            metrics['atr_exits'] = int(atr_exits)
                        except:
                            pass
            
            # 计算ATR止盈成功率
            if metrics['total_trades'] > 0:
                metrics['atr_success_rate'] = metrics['atr_exits'] / metrics['total_trades']
            
        except Exception as e:
            self.logger.error(f"解析 {pair} 回测结果失败: {e}")
        
        return metrics

    def _get_default_metrics(self, pair: str) -> Dict:
        """获取默认指标"""
        return {
            'pair': pair,
            'total_trades': 0,
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'atr_exits': 0,
            'atr_success_rate': 0.0,
            'liquidity': 0.0,
            'sector': self._get_pair_sector(pair),
            'score': 0.0
        }

    def _get_pair_sector(self, pair: str) -> str:
        """获取交易对所属板块"""
        base = pair.split('/')[0]
        
        for sector, coins in self.sectors.items():
            if base in coins:
                return sector
        
        return 'OTHER'

    def calculate_pair_score(self, metrics: Dict) -> float:
        """
        计算交易对综合评分
        
        Args:
            metrics: 交易对指标字典
            
        Returns:
            综合评分(0-100)
        """
        score = 0.0
        
        # 胜率评分 (0-40分)
        win_rate_score = min(metrics['win_rate'] * 100, 40) * self.weights['win_rate']
        
        # 利润因子评分 (0-30分)
        pf_score = min(metrics['profit_factor'] * 30, 30) * self.weights['profit_factor']
        
        # ATR止盈成功率评分 (0-20分)
        atr_score = min(metrics['atr_success_rate'] * 100, 20) * self.weights['atr_success_rate']
        
        # 流动性评分 (0-10分)
        liquidity_score = min(metrics['liquidity'] / 100_000_000 * 10, 10) * self.weights['liquidity']
        
        score = win_rate_score + pf_score + atr_score + liquidity_score
        
        return round(score, 2)

    def filter_pairs_by_thresholds(self, pair_metrics: List[Dict]) -> List[Dict]:
        """
        根据阈值筛选交易对
        
        Args:
            pair_metrics: 交易对指标列表
            
        Returns:
            筛选后的交易对列表
        """
        filtered = []
        
        for metrics in pair_metrics:
            if (metrics['win_rate'] >= self.thresholds['min_win_rate'] and
                metrics['profit_factor'] >= self.thresholds['min_profit_factor'] and
                metrics['atr_success_rate'] >= self.thresholds['min_atr_success_rate'] and
                metrics['liquidity'] >= self.thresholds['min_volume_usdt']):
                
                filtered.append(metrics)
        
        return filtered

    def ensure_sector_diversity(self, top_pairs: List[Dict], target_count: int = 10) -> List[Dict]:
        """
        确保板块多样性
        
        Args:
            top_pairs: 排序后的交易对列表
            target_count: 目标交易对数量
            
        Returns:
            板块多样化后的交易对列表
        """
        selected = []
        sector_count = {}
        max_per_sector = max(2, target_count // len(self.sectors))
        
        for pair_metrics in top_pairs:
            sector = pair_metrics['sector']
            current_count = sector_count.get(sector, 0)
            
            if current_count < max_per_sector and len(selected) < target_count:
                selected.append(pair_metrics)
                sector_count[sector] = current_count + 1
        
        # 如果还没达到目标数量，按评分继续添加
        for pair_metrics in top_pairs:
            if len(selected) >= target_count:
                break
            if pair_metrics not in selected:
                selected.append(pair_metrics)
        
        return selected[:target_count]

    def select_optimal_pairs(self, target_count: int = 10, use_candidate_pool: bool = True) -> List[str]:
        """
        选择最优交易对

        Args:
            target_count: 目标交易对数量
            use_candidate_pool: 是否使用候选池（True=从50个候选中选择，False=全市场筛选）

        Returns:
            最优交易对列表
        """
        self.logger.info("开始ADL策略动态交易对筛选...")

        # 1. 获取交易对候选池
        if use_candidate_pool:
            candidate_pairs = self.get_adl_suitable_pairs()
            self.logger.info(f"使用ADL策略候选池: {len(candidate_pairs)} 个交易对")
        else:
            candidate_pairs = self.get_binance_usdc_pairs()
            self.logger.info(f"使用全市场USDC-M交易对: {len(candidate_pairs)} 个")

        if not candidate_pairs:
            self.logger.error("未获取到任何候选交易对")
            return []

        # 2. 流动性预筛选（快速过滤）
        self.logger.info("执行流动性预筛选...")
        liquid_pairs = []

        for i, pair in enumerate(candidate_pairs):
            self.logger.info(f"检查流动性 {i+1}/{len(candidate_pairs)}: {pair}")

            try:
                liquidity = self.get_pair_liquidity(pair)
                if liquidity >= self.thresholds['min_volume_usdt']:
                    liquid_pairs.append(pair)
                    self.logger.info(f"[PASS] {pair} 流动性合格: {liquidity:,.0f} USDT")
                else:
                    self.logger.info(f"[FAIL] {pair} 流动性不足: {liquidity:,.0f} USDT")
            except Exception as e:
                self.logger.warning(f"[WARN] {pair} 流动性检查失败: {e}")

        self.logger.info(f"流动性筛选结果: {len(liquid_pairs)}/{len(candidate_pairs)} 个交易对通过")

        if not liquid_pairs:
            self.logger.error("没有交易对通过流动性筛选")
            return []

        # 3. 回测评估（耗时操作）
        self.logger.info("开始回测评估...")
        pair_metrics = []

        for i, pair in enumerate(liquid_pairs):
            self.logger.info(f"回测评估 {i+1}/{len(liquid_pairs)}: {pair}")

            try:
                # 运行回测
                metrics = self.run_pair_backtest(pair)

                # 重新获取流动性（确保数据一致性）
                metrics['liquidity'] = self.get_pair_liquidity(pair)

                # 计算综合评分
                metrics['score'] = self.calculate_pair_score(metrics)

                pair_metrics.append(metrics)

                self.logger.info(f"[DONE] {pair} 评估完成 - 评分: {metrics['score']:.2f}, "
                               f"胜率: {metrics['win_rate']:.1%}, "
                               f"利润因子: {metrics['profit_factor']:.2f}")

            except Exception as e:
                self.logger.error(f"[ERROR] {pair} 回测评估失败: {e}")

        if not pair_metrics:
            self.logger.error("没有交易对完成回测评估")
            return []

        # 4. 根据阈值筛选
        qualified_pairs = self.filter_pairs_by_thresholds(pair_metrics)
        self.logger.info(f"阈值筛选结果: {len(qualified_pairs)}/{len(pair_metrics)} 个交易对合格")

        if not qualified_pairs:
            self.logger.warning("没有交易对通过阈值筛选，降低标准重新筛选...")
            # 降低标准重新筛选
            self.thresholds['min_win_rate'] = 0.50
            self.thresholds['min_profit_factor'] = 0.60
            self.thresholds['min_atr_success_rate'] = 0.30
            qualified_pairs = self.filter_pairs_by_thresholds(pair_metrics)
            self.logger.info(f"降低标准后: {len(qualified_pairs)} 个交易对合格")

        # 5. 按评分排序
        qualified_pairs.sort(key=lambda x: x['score'], reverse=True)

        # 6. 确保板块多样性
        final_pairs = self.ensure_sector_diversity(qualified_pairs, target_count)

        # 7. 返回交易对符号列表
        selected_symbols = [pair['pair'] for pair in final_pairs]

        self.logger.info(f"最终选择的交易对: {selected_symbols}")

        return selected_symbols

    def generate_evaluation_report(self, pair_metrics: List[Dict]) -> str:
        """
        生成交易对评估报告

        Args:
            pair_metrics: 交易对指标列表

        Returns:
            评估报告文本
        """
        report = []
        report.append("=" * 80)
        report.append("ADL策略动态交易对筛选评估报告")
        report.append("=" * 80)
        report.append(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"评估交易对数量: {len(pair_metrics)}")
        report.append("")

        # 排序
        sorted_pairs = sorted(pair_metrics, key=lambda x: x['score'], reverse=True)

        # 前10名详细报告
        report.append("前10名交易对详细评估:")
        report.append("-" * 80)
        report.append(f"{'排名':<4} {'交易对':<15} {'评分':<8} {'胜率':<8} {'利润因子':<10} {'ATR成功率':<10} {'板块':<12}")
        report.append("-" * 80)

        for i, pair in enumerate(sorted_pairs[:10], 1):
            report.append(
                f"{i:<4} {pair['pair']:<15} {pair['score']:<8.2f} "
                f"{pair['win_rate']:<8.1%} {pair['profit_factor']:<10.2f} "
                f"{pair['atr_success_rate']:<10.1%} {pair['sector']:<12}"
            )

        # 板块分布统计
        report.append("")
        report.append("板块分布统计:")
        report.append("-" * 40)

        sector_stats = {}
        for pair in sorted_pairs[:10]:
            sector = pair['sector']
            sector_stats[sector] = sector_stats.get(sector, 0) + 1

        for sector, count in sector_stats.items():
            report.append(f"{sector:<20} {count} 个")

        # 筛选统计
        report.append("")
        report.append("筛选统计:")
        report.append("-" * 40)

        qualified = len([p for p in pair_metrics if p['score'] > 0])
        high_score = len([p for p in pair_metrics if p['score'] >= 50])

        report.append(f"总评估数量: {len(pair_metrics)}")
        report.append(f"合格交易对: {qualified}")
        report.append(f"高分交易对(≥50分): {high_score}")

        return "\n".join(report)

    def update_config_pairs(self, selected_pairs: List[str]) -> bool:
        """
        更新配置文件中的交易对列表

        Args:
            selected_pairs: 选中的交易对列表

        Returns:
            更新是否成功
        """
        try:
            # 读取配置文件
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 备份原配置
            backup_path = f"{self.config_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)

            # 更新交易对列表
            config['exchange']['pair_whitelist'] = selected_pairs

            # 保存更新后的配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)

            self.logger.info(f"配置文件已更新，备份保存为: {backup_path}")
            return True

        except Exception as e:
            self.logger.error(f"更新配置文件失败: {e}")
            return False
