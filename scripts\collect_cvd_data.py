#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CVD数据收集脚本

基于CVD.md文档第5章要求，实现完整的数据收集和处理流程：
1. 获取Bybit逐笔交易数据
2. 计算多层CVD特征
3. 准备FreqTrade回测数据
"""

import sys
import logging
from datetime import datetime, timedelta
from pathlib import Path
import argparse

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from data.sources.bybit_trades import BybitTradesCollector
from indicators.cvd_calculator import MultiLayerCVDCalculator, CVDThresholds

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('cvd_data_collection.log')
        ]
    )

def collect_trades_data(symbol: str, days: int = 30) -> bool:
    """
    收集交易数据
    
    Args:
        symbol: 交易对符号
        days: 收集天数
        
    Returns:
        是否成功
    """
    logger = logging.getLogger('collect_trades_data')
    
    try:
        # 初始化收集器
        collector = BybitTradesCollector(
            symbol=symbol,
            storage_dir="user_data/data/trades"
        )
        
        # 设置时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        logger.info(f"开始收集 {symbol} 交易数据: {start_date} 到 {end_date}")
        
        # 收集数据
        trades_df = collector.collect_historical_trades(start_date, end_date)
        
        if trades_df.empty:
            logger.error("未收集到任何交易数据")
            return False
            
        # 显示摘要
        summary = collector.get_data_summary()
        logger.info(f"数据收集完成: {summary}")
        
        return True
        
    except Exception as e:
        logger.error(f"交易数据收集失败: {e}")
        return False

def process_cvd_features(symbol: str, timeframe: str = '5T', enable_dynamic: bool = True) -> bool:
    """
    处理CVD特征

    Args:
        symbol: 交易对符号
        timeframe: 时间周期
        enable_dynamic: 启用动态阈值

    Returns:
        是否成功
    """
    logger = logging.getLogger('process_cvd_features')

    try:
        # 初始化计算器（支持动态阈值）
        thresholds = CVDThresholds(enable_dynamic=enable_dynamic)
        calculator = MultiLayerCVDCalculator(thresholds=thresholds)

        if enable_dynamic:
            logger.info("启用动态CVD阈值调整")
        
        # 加载交易数据
        collector = BybitTradesCollector(
            symbol=symbol,
            storage_dir="user_data/data/trades"
        )
        
        trades_df = collector._load_all_data()
        
        if trades_df.empty:
            logger.error(f"未找到 {symbol} 的交易数据")
            return False
            
        logger.info(f"开始处理 {len(trades_df)} 条交易记录")
        
        # 计算CVD特征
        cvd_data = calculator.process_trades_to_cvd(trades_df, timeframe)
        
        if cvd_data.empty:
            logger.error("CVD特征计算失败")
            return False
            
        # 保存CVD数据
        output_dir = Path("user_data/data/cvd_features")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        output_file = output_dir / f"{symbol}_{timeframe}_cvd.parquet"
        cvd_data.to_parquet(output_file)
        
        logger.info(f"CVD特征已保存: {output_file}")
        logger.info(f"生成 {len(cvd_data)} 个时间周期的CVD数据")
        
        # 显示CVD摘要
        summary = {}
        for trade_type in ['retail', 'pro', 'whale']:
            cvd_col = f'cvd_{trade_type}'
            if cvd_col in cvd_data.columns:
                summary[f'{trade_type}_cvd'] = {
                    'min': float(cvd_data[cvd_col].min()),
                    'max': float(cvd_data[cvd_col].max()),
                    'final': float(cvd_data[cvd_col].iloc[-1])
                }

        # 背离信号统计
        if 'bullish_divergence' in cvd_data.columns:
            summary['bullish_signals'] = int(cvd_data['bullish_divergence'].sum())
        if 'bearish_divergence' in cvd_data.columns:
            summary['bearish_signals'] = int(cvd_data['bearish_divergence'].sum())

        # 阈值统计
        threshold_stats = calculator.get_threshold_statistics()
        summary['threshold_statistics'] = threshold_stats

        logger.info(f"CVD摘要: {summary}")

        if enable_dynamic and threshold_stats.get('adjustment_points', 0) > 0:
            logger.info(f"动态阈值调整统计:")
            logger.info(f"  散户阈值范围: ${threshold_stats.get('dynamic_retail_min', 0):.0f} - ${threshold_stats.get('dynamic_retail_max', 0):.0f}")
            logger.info(f"  巨鲸阈值范围: ${threshold_stats.get('dynamic_whale_min', 0):.0f} - ${threshold_stats.get('dynamic_whale_max', 0):.0f}")
            logger.info(f"  调整点数量: {threshold_stats.get('adjustment_points', 0)}")
        
        return True
        
    except Exception as e:
        logger.error(f"CVD特征处理失败: {e}")
        return False

def test_cvd_provider(pair: str = "BTC/USDT") -> bool:
    """
    测试CVD数据提供者
    
    Args:
        pair: 交易对
        
    Returns:
        是否成功
    """
    logger = logging.getLogger('test_cvd_provider')
    
    try:
        import sys
        sys.path.append('freqtrade-bot')
        from user_data.data_providers.cvd_data_provider import CVDDataProvider
        
        # 模拟FreqTrade配置
        config = {
            'datadir': 'user_data/data',
            'timeframe': '5m'
        }
        
        # 初始化数据提供者
        provider = CVDDataProvider(config)
        
        # 获取可用交易对
        available_pairs = provider.get_available_pairs()
        logger.info(f"可用交易对: {available_pairs}")
        
        if pair not in available_pairs:
            logger.warning(f"{pair} 不在可用交易对中，使用第一个可用交易对")
            if available_pairs:
                pair = available_pairs[0]
            else:
                logger.error("没有可用的交易对")
                return False
                
        # 获取合并数据
        merged_data = provider.get_pair_dataframe(pair, '5m')
        
        if merged_data.empty:
            logger.error("未获取到合并数据")
            return False
            
        logger.info(f"合并数据形状: {merged_data.shape}")
        logger.info(f"列名: {list(merged_data.columns)}")
        
        # 检查CVD列
        cvd_columns = [col for col in merged_data.columns if 'cvd' in col.lower()]
        logger.info(f"CVD相关列: {cvd_columns}")
        
        # 获取摘要
        summary = provider.get_cvd_summary(pair, '5m')
        logger.info(f"CVD摘要: {summary}")
        
        return True
        
    except Exception as e:
        logger.error(f"CVD数据提供者测试失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CVD数据收集和处理')
    parser.add_argument('--symbol', default='BTCUSDT', help='交易对符号')
    parser.add_argument('--days', type=int, default=30, help='收集天数')
    parser.add_argument('--timeframe', default='5T', help='时间周期')
    parser.add_argument('--skip-collect', action='store_true', help='跳过数据收集')
    parser.add_argument('--skip-process', action='store_true', help='跳过特征处理')
    parser.add_argument('--test-only', action='store_true', help='仅测试数据提供者')
    parser.add_argument('--disable-dynamic', action='store_true', help='禁用动态阈值调整')
    
    args = parser.parse_args()
    
    setup_logging()
    logger = logging.getLogger('main')
    
    logger.info("=== CVD数据收集和处理开始 ===")
    
    success = True
    
    if args.test_only:
        # 仅测试数据提供者
        pair = args.symbol.replace('USDT', '/USDT')
        success = test_cvd_provider(pair)
    else:
        # 完整流程
        if not args.skip_collect:
            logger.info("步骤1: 收集交易数据")
            success = collect_trades_data(args.symbol, args.days)
            
        if success and not args.skip_process:
            logger.info("步骤2: 处理CVD特征")
            enable_dynamic = not args.disable_dynamic
            success = process_cvd_features(args.symbol, args.timeframe, enable_dynamic)
            
        if success:
            logger.info("步骤3: 测试数据提供者")
            pair = args.symbol.replace('USDT', '/USDT')
            success = test_cvd_provider(pair)
    
    if success:
        logger.info("=== CVD数据收集和处理完成 ===")
        print("\n✅ CVD数据收集和处理成功完成！")
        print("\n下一步可以运行FreqTrade回测:")
        print(f"freqtrade backtesting --strategy PanicAbsorptionStrategy --timeframe 5m")
    else:
        logger.error("=== CVD数据收集和处理失败 ===")
        print("\n❌ CVD数据收集和处理失败，请检查日志")
        sys.exit(1)

if __name__ == "__main__":
    main()
