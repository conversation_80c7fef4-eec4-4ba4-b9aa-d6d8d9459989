#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Binance历史数据下载器

基于Binance公共数据仓库下载180天的历史Klines数据，
从中计算TakeFlow所需的Taker买卖量指标。

Klines数据包含：
- Taker buy base asset volume - Taker买入基础资产量  
- Taker buy quote asset volume - Taker买入计价资产量

计算公式：
- Taker买入量 = Taker buy quote asset volume
- Taker卖出量 = Quote asset volume - Taker buy quote asset volume  
- Taker比率 = Taker买入量 / Taker卖出量
"""

import os
import time
import logging
import requests
import pandas as pd
import sqlite3
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import zipfile
import io

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('BinanceHistoryDownloader')


class BinanceHistoryDownloader:
    """Binance历史数据下载器"""
    
    def __init__(self, 
                 storage_dir: str = "user_data/data/takeflow",
                 days: int = 180):
        """
        初始化历史数据下载器
        
        Args:
            storage_dir: 数据存储目录
            days: 下载天数，默认180天
        """
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        self.days = days
        self.base_url = "https://data.binance.vision/data/futures/um/daily/klines"
        
        # 10个主要币种
        self.symbols = [
            'BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT',
            'BNBUSDT', 'XRPUSDT', 'MATICUSDT', 'LINKUSDT', 'AVAXUSDT'
        ]
        
        # 初始化数据库
        self.db_path = self.storage_dir / "takeflow_data.db"
        self._init_database()
        
        logger.info(f"初始化Binance历史数据下载器")
        logger.info(f"目标币种: {self.symbols}")
        logger.info(f"下载天数: {days}天")
        logger.info(f"数据源: Binance公共数据仓库")
    
    def _init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS takeflow_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp INTEGER NOT NULL,
                    symbol TEXT NOT NULL,
                    last_price REAL,
                    index_price REAL,
                    mark_price REAL,
                    basis REAL,
                    taker_buy_vol REAL,
                    taker_sell_vol REAL,
                    taker_ratio REAL,
                    funding_rate REAL,
                    next_funding_time INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(timestamp, symbol)
                )
            ''')
            
            # 创建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_symbol_timestamp ON takeflow_data(symbol, timestamp)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON takeflow_data(timestamp)')
    
    def _generate_date_range(self) -> List[str]:
        """生成日期范围列表"""
        # 结束日期设为昨天，因为今天的数据可能还没有
        end_date = datetime.now() - timedelta(days=1)
        start_date = end_date - timedelta(days=self.days)

        dates = []
        current_date = start_date

        while current_date <= end_date:
            dates.append(current_date.strftime('%Y-%m-%d'))
            current_date += timedelta(days=1)

        return dates
    
    def _download_klines_data(self, symbol: str, date: str) -> Optional[pd.DataFrame]:
        """
        下载单个币种单日的Klines数据
        
        Args:
            symbol: 交易对符号
            date: 日期字符串 (YYYY-MM-DD)
            
        Returns:
            Klines数据DataFrame
        """
        url = f"{self.base_url}/{symbol}/5m/{symbol}-5m-{date}.zip"
        
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # 解压ZIP文件
            with zipfile.ZipFile(io.BytesIO(response.content)) as zip_file:
                csv_filename = f"{symbol}-5m-{date}.csv"
                
                if csv_filename in zip_file.namelist():
                    with zip_file.open(csv_filename) as csv_file:
                        # Binance Klines CSV格式
                        columns = [
                            'open_time', 'open', 'high', 'low', 'close', 'volume',
                            'close_time', 'quote_asset_volume', 'number_of_trades',
                            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
                        ]
                        
                        df = pd.read_csv(csv_file, names=columns)
                        
                        # 计算TakeFlow指标 - 确保数据类型为数值
                        df['taker_buy_vol'] = pd.to_numeric(df['taker_buy_quote_asset_volume'], errors='coerce')
                        df['taker_sell_vol'] = pd.to_numeric(df['quote_asset_volume'], errors='coerce') - df['taker_buy_vol']
                        df['taker_ratio'] = df['taker_buy_vol'] / df['taker_sell_vol']
                        
                        # 处理除零错误
                        df['taker_ratio'] = df['taker_ratio'].replace([float('inf'), -float('inf')], 1.0)
                        df['taker_ratio'] = df['taker_ratio'].fillna(1.0)
                        
                        # 添加基本信息
                        df['symbol'] = symbol
                        df['timestamp'] = df['open_time']
                        df['last_price'] = df['close']
                        
                        return df[['timestamp', 'symbol', 'last_price', 'taker_buy_vol', 'taker_sell_vol', 'taker_ratio']]
                        
        except requests.exceptions.RequestException as e:
            logger.debug(f"下载 {symbol} {date} 失败: {e}")
            return None
        except Exception as e:
            logger.error(f"处理 {symbol} {date} 数据失败: {e}")
            return None
    
    def _download_symbol_history(self, symbol: str) -> int:
        """
        下载单个币种的历史数据
        
        Args:
            symbol: 交易对符号
            
        Returns:
            下载的记录数量
        """
        dates = self._generate_date_range()
        total_records = 0
        
        logger.info(f"开始下载 {symbol} 的 {len(dates)} 天历史数据...")
        
        # 使用线程池并行下载
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_date = {
                executor.submit(self._download_klines_data, symbol, date): date 
                for date in dates
            }
            
            all_data = []
            
            for future in as_completed(future_to_date):
                date = future_to_date[future]
                try:
                    df = future.result()
                    if df is not None and not df.empty:
                        all_data.append(df)
                        logger.debug(f"✅ {symbol} {date}: {len(df)} 条记录")
                    else:
                        logger.debug(f"❌ {symbol} {date}: 无数据")
                except Exception as e:
                    logger.error(f"❌ {symbol} {date}: {e}")
        
        # 合并所有数据
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # 保存到数据库
            total_records = self._save_to_database(combined_df)
            
            logger.info(f"✅ {symbol}: 成功下载 {total_records} 条记录")
        else:
            logger.warning(f"❌ {symbol}: 未获取到任何数据")
            
        return total_records
    
    def _save_to_database(self, df: pd.DataFrame) -> int:
        """
        保存数据到数据库
        
        Args:
            df: 数据DataFrame
            
        Returns:
            保存的记录数量
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                saved_count = 0
                
                for _, row in df.iterrows():
                    try:
                        conn.execute('''
                            INSERT OR REPLACE INTO takeflow_data 
                            (timestamp, symbol, last_price, index_price, mark_price, basis,
                             taker_buy_vol, taker_sell_vol, taker_ratio, funding_rate, next_funding_time)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            int(row['timestamp']), row['symbol'], float(row['last_price']), 
                            None, None, None,  # index_price, mark_price, basis 暂时为空
                            float(row['taker_buy_vol']), float(row['taker_sell_vol']), 
                            float(row['taker_ratio']), None, None  # funding_rate, next_funding_time 暂时为空
                        ))
                        saved_count += 1
                    except Exception as e:
                        logger.debug(f"保存单条记录失败: {e}")
                        continue
                
                conn.commit()
                return saved_count
                
        except Exception as e:
            logger.error(f"保存数据到数据库失败: {e}")
            return 0
    
    def download_all_history(self):
        """下载所有币种的历史数据"""
        logger.info("=" * 60)
        logger.info("开始批量下载Binance历史数据...")
        logger.info(f"数据源: {self.base_url}")
        logger.info(f"时间范围: {self.days}天")
        logger.info("=" * 60)
        
        total_records = 0
        successful_symbols = []
        failed_symbols = []
        
        for i, symbol in enumerate(self.symbols, 1):
            logger.info(f"[{i}/{len(self.symbols)}] 处理 {symbol}...")
            
            try:
                records = self._download_symbol_history(symbol)
                
                if records > 0:
                    total_records += records
                    successful_symbols.append(symbol)
                else:
                    failed_symbols.append(symbol)
                
                # 避免请求过于频繁
                time.sleep(1)
                
            except Exception as e:
                failed_symbols.append(symbol)
                logger.error(f"❌ {symbol}: 下载失败 - {e}")
                continue
        
        # 下载总结
        logger.info("=" * 60)
        logger.info("历史数据下载完成！")
        logger.info(f"总计下载: {total_records:,} 条记录")
        logger.info(f"成功币种 ({len(successful_symbols)}): {successful_symbols}")
        
        if failed_symbols:
            logger.warning(f"失败币种 ({len(failed_symbols)}): {failed_symbols}")
        
        # 生成数据报告
        self._generate_data_report()
        
        return total_records, successful_symbols, failed_symbols
    
    def _generate_data_report(self):
        """生成数据报告"""
        logger.info("=" * 60)
        logger.info("数据库统计报告:")
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                for symbol in self.symbols:
                    cursor = conn.execute('''
                        SELECT 
                            COUNT(*) as total_records,
                            MIN(timestamp) as start_timestamp,
                            MAX(timestamp) as end_timestamp,
                            AVG(taker_ratio) as avg_taker_ratio
                        FROM takeflow_data 
                        WHERE symbol = ?
                    ''', (symbol,))
                    
                    result = cursor.fetchone()
                    
                    if result and result[0] > 0:
                        start_time = datetime.fromtimestamp(result[1] / 1000).strftime('%Y-%m-%d')
                        end_time = datetime.fromtimestamp(result[2] / 1000).strftime('%Y-%m-%d')
                        avg_ratio = round(result[3], 4) if result[3] else 0
                        
                        logger.info(f"{symbol}: {result[0]:,} 条记录 ({start_time} ~ {end_time}) 平均Taker比率: {avg_ratio}")
                    else:
                        logger.info(f"{symbol}: 无数据")
                        
        except Exception as e:
            logger.error(f"生成数据报告失败: {e}")
        
        logger.info("=" * 60)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Binance历史数据下载器')
    parser.add_argument('--storage-dir', default='user_data/data/takeflow',
                       help='数据存储目录')
    parser.add_argument('--days', type=int, default=180,
                       help='下载天数，默认180天')
    
    args = parser.parse_args()
    
    # 创建下载器
    downloader = BinanceHistoryDownloader(
        storage_dir=args.storage_dir,
        days=args.days
    )
    
    # 下载历史数据
    total_records, successful_symbols, failed_symbols = downloader.download_all_history()
    
    # 最终总结
    print("\n" + "=" * 60)
    print("🎯 Binance历史数据下载完成！")
    print(f"📊 总计下载: {total_records:,} 条记录")
    print(f"✅ 成功币种: {len(successful_symbols)}/{len(downloader.symbols)}")
    
    if failed_symbols:
        print(f"❌ 失败币种: {failed_symbols}")
    
    print("\n💡 下一步:")
    print("1. 下载对应的FreqTrade期货OHLCV数据")
    print("2. 测试TakeFlow数据提供者")
    print("3. 开始策略回测")
    print("=" * 60)


if __name__ == "__main__":
    main()
