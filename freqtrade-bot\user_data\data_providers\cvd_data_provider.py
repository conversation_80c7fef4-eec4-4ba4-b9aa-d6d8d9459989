#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CVD自定义数据提供者

基于CVD.md文档第5.3章要求，实现自定义IDataProvider，
将多层CVD数据与标准OHLCV数据合并，用于FreqTrade策略回测。
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, Optional, Any, List
from freqtrade.data.dataprovider import DataProvider
from freqtrade.data.history import load_pair_history
from freqtrade.configuration import Configuration
from freqtrade.enums import CandleType

from indicators.cvd_calculator import MultiLayerCVDCalculator, CVDThresholds
from data.sources.bybit_trades import BybitTradesCollector

class CVDDataProvider:
    """
    CVD数据提供者
    
    基于CVD文档第5.3章的专业解决方案，创建自定义数据提供者。
    预先加载并合并OHLCV数据和CVD特征数据，确保无未来数据泄露。
    """
    
    def __init__(self,
                 config: Dict[str, Any],
                 trades_data_dir: str = "user_data/data/trades",
                 cvd_cache_dir: str = "user_data/data/cvd_cache",
                 enable_dynamic_thresholds: bool = True):
        """
        初始化CVD数据提供者

        Args:
            config: FreqTrade配置
            trades_data_dir: 交易数据目录
            cvd_cache_dir: CVD缓存目录
            enable_dynamic_thresholds: 启用动态CVD阈值调整
        """
        self.config = config
        self.trades_data_dir = Path(trades_data_dir)
        self.cvd_cache_dir = Path(cvd_cache_dir)
        self.cvd_cache_dir.mkdir(parents=True, exist_ok=True)

        self.logger = logging.getLogger(self.__class__.__name__)

        # 初始化CVD计算器，支持动态阈值
        cvd_thresholds = CVDThresholds(enable_dynamic=enable_dynamic_thresholds)
        self.cvd_calculator = MultiLayerCVDCalculator(thresholds=cvd_thresholds)

        # 缓存已处理的CVD数据
        self._cvd_cache: Dict[str, pd.DataFrame] = {}
        
    def _get_cache_filename(self, pair: str, timeframe: str) -> Path:
        """获取CVD缓存文件名"""
        return self.cvd_cache_dir / f"{pair}_{timeframe}_cvd.parquet"
        
    def _load_trades_data(self, pair: str) -> pd.DataFrame:
        """
        加载真实逐笔交易数据

        Args:
            pair: 交易对

        Returns:
            真实交易数据DataFrame
        """
        # 转换FreqTrade格式到符号格式
        symbol = pair.replace('/', '').replace(':USDT', 'USDT')

        # 优先使用真实下载的数据
        real_trades_file = Path("user_data/data/real_trades") / f"{symbol}_real_trades_30days.feather"

        if real_trades_file.exists():
            self.logger.info(f"加载真实逐笔交易数据: {real_trades_file}")
            try:
                df = pd.read_feather(real_trades_file)
                self.logger.info(f"成功加载 {len(df):,} 条真实交易记录")
                return df
            except Exception as e:
                self.logger.error(f"加载真实交易数据失败: {e}")

        # 回退到Bybit收集器（如果真实数据不可用）
        self.logger.warning(f"真实交易数据不可用，尝试使用Bybit收集器")
        collector = BybitTradesCollector(
            symbol=symbol,
            storage_dir=str(self.trades_data_dir)
        )

        return collector._load_all_data()
        
    def _calculate_cvd_features(self, 
                               pair: str, 
                               timeframe: str,
                               force_recalculate: bool = False) -> pd.DataFrame:
        """
        计算CVD特征
        
        Args:
            pair: 交易对
            timeframe: 时间周期
            force_recalculate: 强制重新计算
            
        Returns:
            CVD特征DataFrame
        """
        # 转换交易对格式
        symbol = pair.replace('/', '').replace(':USDT', 'USDT')

        # 优先使用预处理的真实CVD数据
        real_cvd_file = Path("user_data/data/cvd") / f"{symbol}_cvd_freqtrade.feather"

        if not force_recalculate and real_cvd_file.exists():
            try:
                cvd_data = pd.read_feather(real_cvd_file)
                # 确保时间索引
                if 'timestamp' in cvd_data.columns:
                    cvd_data.set_index('timestamp', inplace=True)
                elif cvd_data.index.name != 'timestamp':
                    cvd_data.index = pd.to_datetime(cvd_data.index)

                self.logger.info(f"加载预处理的真实CVD数据: {len(cvd_data)} 条记录")
                self.logger.info(f"数据时间范围: {cvd_data.index.min()} 到 {cvd_data.index.max()}")
                return cvd_data
            except Exception as e:
                self.logger.warning(f"加载预处理CVD数据失败，重新计算: {e}")

        # 回退到缓存
        cache_file = self._get_cache_filename(pair, timeframe)

        # 检查缓存
        if not force_recalculate and cache_file.exists():
            self.logger.info(f"从缓存加载CVD数据: {cache_file}")
            return pd.read_parquet(cache_file)
            
        # 加载交易数据
        trades_df = self._load_trades_data(pair)
        
        if trades_df.empty:
            self.logger.warning(f"未找到 {pair} 的交易数据")
            return pd.DataFrame()
            
        # 加载对应的OHLCV数据用于动态阈值计算
        market_data = None
        if self.cvd_calculator.thresholds.enable_dynamic:
            try:
                market_data = load_pair_history(
                    pair=pair,
                    timeframe=timeframe,
                    datadir=Path(self.config.get('datadir', 'user_data/data')),
                    candle_type=CandleType.SPOT
                )
                if not market_data.empty:
                    self.logger.info(f"加载市场数据用于动态阈值计算: {len(market_data)} 条记录")
            except Exception as e:
                self.logger.warning(f"加载市场数据失败，使用静态阈值: {e}")

        # 计算CVD特征（支持动态阈值）
        self.logger.info(f"计算 {pair} {timeframe} 的CVD特征")
        cvd_data = self.cvd_calculator.process_trades_to_cvd(
            trades_df,
            timeframe=timeframe,
            market_data=market_data
        )

        # 记录阈值统计信息
        threshold_stats = self.cvd_calculator.get_threshold_statistics()
        self.logger.info(f"CVD阈值统计: {threshold_stats}")
        
        # 保存到缓存
        if not cvd_data.empty:
            cvd_data.to_parquet(cache_file)
            self.logger.info(f"CVD数据已缓存: {cache_file}")
            
        return cvd_data
        
    def get_pair_dataframe(self, 
                          pair: str, 
                          timeframe: str,
                          candle_type: CandleType = CandleType.SPOT) -> pd.DataFrame:
        """
        获取合并后的交易对数据
        
        Args:
            pair: 交易对
            timeframe: 时间周期  
            candle_type: K线类型
            
        Returns:
            合并OHLCV和CVD数据的DataFrame
        """
        cache_key = f"{pair}_{timeframe}_{candle_type.value}"
        
        if cache_key in self._cvd_cache:
            return self._cvd_cache[cache_key]
            
        # 加载标准OHLCV数据
        try:
            ohlcv_data = load_pair_history(
                pair=pair,
                timeframe=timeframe,
                datadir=Path(self.config.get('datadir', 'user_data/data')),
                candle_type=candle_type
            )
        except Exception as e:
            self.logger.error(f"加载OHLCV数据失败: {e}")
            return pd.DataFrame()
            
        if ohlcv_data.empty:
            self.logger.warning(f"未找到 {pair} {timeframe} 的OHLCV数据")
            return pd.DataFrame()
            
        # 加载CVD数据
        cvd_data = self._calculate_cvd_features(pair, timeframe)
        
        if cvd_data.empty:
            self.logger.warning(f"未找到 {pair} 的CVD数据，返回纯OHLCV数据")
            self._cvd_cache[cache_key] = ohlcv_data
            return ohlcv_data
            
        # 合并数据 - 基于CVD文档5.3章的合并逻辑
        merged_data = self._merge_ohlcv_cvd(ohlcv_data, cvd_data)
        
        # 缓存结果
        self._cvd_cache[cache_key] = merged_data
        
        return merged_data
        
    def _merge_ohlcv_cvd(self, 
                        ohlcv_df: pd.DataFrame, 
                        cvd_df: pd.DataFrame) -> pd.DataFrame:
        """
        合并OHLCV和CVD数据
        
        Args:
            ohlcv_df: OHLCV数据
            cvd_df: CVD数据
            
        Returns:
            合并后的DataFrame
        """
        # 确保时间索引对齐
        ohlcv_df = ohlcv_df.copy()
        cvd_df = cvd_df.copy()
        
        # 标准化时间索引处理
        # 处理OHLCV数据索引
        if 'date' not in ohlcv_df.columns:
            ohlcv_df.reset_index(inplace=True)
        if 'date' in ohlcv_df.columns:
            ohlcv_df['date'] = pd.to_datetime(ohlcv_df['date'])
            # 移除时区信息以保持一致性
            if ohlcv_df['date'].dt.tz is not None:
                ohlcv_df['date'] = ohlcv_df['date'].dt.tz_localize(None)
            ohlcv_df.set_index('date', inplace=True)

        # 处理CVD数据索引
        if not isinstance(cvd_df.index, pd.DatetimeIndex):
            if 'timestamp' in cvd_df.columns:
                cvd_df.set_index('timestamp', inplace=True)
            elif cvd_df.index.name == 'timestamp':
                pass  # 已经是正确的索引
            else:
                cvd_df.reset_index(inplace=True)
                # 查找时间列
                time_col = None
                for col in cvd_df.columns:
                    if 'time' in col.lower() or 'date' in col.lower():
                        time_col = col
                        break
                if time_col:
                    cvd_df.set_index(time_col, inplace=True)

        # 确保CVD索引是datetime类型且无时区
        if isinstance(cvd_df.index, pd.DatetimeIndex):
            if cvd_df.index.tz is not None:
                cvd_df.index = cvd_df.index.tz_localize(None)
        else:
            cvd_df.index = pd.to_datetime(cvd_df.index)

        # 重置索引以便合并
        ohlcv_df.reset_index(inplace=True)
        cvd_df.reset_index(inplace=True)

        # 统一列名
        if ohlcv_df.columns[0] != 'date':
            ohlcv_df.rename(columns={ohlcv_df.columns[0]: 'date'}, inplace=True)
        if cvd_df.columns[0] != 'date':
            cvd_df.rename(columns={cvd_df.columns[0]: 'date'}, inplace=True)
            
        # 确保日期列为datetime类型
        ohlcv_df['date'] = pd.to_datetime(ohlcv_df['date'])
        cvd_df['date'] = pd.to_datetime(cvd_df['date'])
        
        # 选择CVD相关列
        cvd_columns = ['date'] + [col for col in cvd_df.columns 
                                 if any(keyword in col.lower() 
                                       for keyword in ['cvd', 'divergence', 'whale', 'retail', 'pro'])]
        
        cvd_subset = cvd_df[cvd_columns].copy()
        
        # 使用左连接确保不引入未来数据
        merged = pd.merge(
            ohlcv_df, 
            cvd_subset, 
            on='date', 
            how='left',
            suffixes=('', '_cvd')
        )
        
        # 前向填充CVD数据的缺失值
        cvd_cols = [col for col in merged.columns if col.startswith('cvd_')]
        merged[cvd_cols] = merged[cvd_cols].fillna(method='ffill')
        
        # 填充剩余的NaN为0
        merged[cvd_cols] = merged[cvd_cols].fillna(0)
        
        # 设置日期为索引
        merged.set_index('date', inplace=True)
        
        self.logger.info(f"数据合并完成: OHLCV {len(ohlcv_df)} 行, CVD {len(cvd_df)} 行, 合并后 {len(merged)} 行")
        
        return merged
        
    def refresh_cvd_data(self, pair: str, timeframe: str) -> None:
        """
        刷新CVD数据缓存
        
        Args:
            pair: 交易对
            timeframe: 时间周期
        """
        cache_key = f"{pair}_{timeframe}_spot"
        if cache_key in self._cvd_cache:
            del self._cvd_cache[cache_key]
            
        # 强制重新计算
        self._calculate_cvd_features(pair, timeframe, force_recalculate=True)
        
    def get_available_pairs(self) -> List[str]:
        """获取可用的交易对列表"""
        available_pairs = []
        
        for file in self.trades_data_dir.glob("*_trades.parquet"):
            # 从文件名提取交易对
            parts = file.stem.split('_')
            if len(parts) >= 2:
                symbol = parts[0]
                # 转换为FreqTrade格式
                if symbol.endswith('USDT'):
                    pair = f"{symbol[:-4]}/USDT"
                    if pair not in available_pairs:
                        available_pairs.append(pair)
                        
        return available_pairs
        
    def get_cvd_summary(self, pair: str, timeframe: str) -> Dict[str, Any]:
        """
        获取CVD数据摘要
        
        Args:
            pair: 交易对
            timeframe: 时间周期
            
        Returns:
            CVD数据摘要
        """
        cvd_data = self._calculate_cvd_features(pair, timeframe)
        
        if cvd_data.empty:
            return {"status": "no_data"}
            
        summary = {
            "total_periods": len(cvd_data),
            "date_range": {
                "start": cvd_data.index.min().strftime('%Y-%m-%d %H:%M:%S'),
                "end": cvd_data.index.max().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        # CVD统计
        for trade_type in ['retail', 'pro', 'whale']:
            cvd_col = f'cvd_{trade_type}'
            if cvd_col in cvd_data.columns:
                summary[f'{trade_type}_cvd_stats'] = {
                    "min": float(cvd_data[cvd_col].min()),
                    "max": float(cvd_data[cvd_col].max()),
                    "final": float(cvd_data[cvd_col].iloc[-1])
                }
                
        # 背离信号统计
        if 'bullish_divergence' in cvd_data.columns:
            summary['bullish_signals'] = int(cvd_data['bullish_divergence'].sum())
        if 'bearish_divergence' in cvd_data.columns:
            summary['bearish_signals'] = int(cvd_data['bearish_divergence'].sum())

        # 添加阈值统计信息
        threshold_stats = self.cvd_calculator.get_threshold_statistics()
        summary['threshold_statistics'] = threshold_stats

        return summary
