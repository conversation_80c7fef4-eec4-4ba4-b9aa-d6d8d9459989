#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Bybit清算数据源实现

基于WaterFallAlpha策略需求，实现Bybit清算数据的实时获取和历史数据处理。
利用Bybit新升级的无节流WebSocket API获取高保真清算数据。
"""

import asyncio
import json
import logging
import gzip
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import pandas as pd
import numpy as np
import websockets
import requests
from pathlib import Path

from data.base import DataSource
from data.structures import OHLCVColumns


@dataclass
class LiquidationEvent:
    """清算事件数据结构"""
    symbol: str
    side: str  # Buy/Sell
    size: float  # 清算数量
    price: float  # 清算价格
    time: datetime  # 清算时间
    value: float  # 清算价值 (size * price)


class BybitLiquidationSource(DataSource):
    """
    Bybit清算数据源
    
    实现清算数据的实时获取和历史数据处理，支持：
    1. WebSocket实时清算数据流
    2. 历史清算数据下载
    3. 清算数据聚合和预处理
    """
    
    def __init__(self, symbols: List[str] = None, storage_dir: str = None):
        """
        初始化Bybit清算数据源
        
        Args:
            symbols: 监控的交易对列表，默认为主要加密货币
            storage_dir: 数据存储目录
        """
        super().__init__()
        self.symbols = symbols or ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'XRPUSDT']
        self.storage_dir = Path(storage_dir) if storage_dir else Path('data/storage/liquidations')
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(self.__class__.__name__)
        self.ws_url = "wss://stream.bybit.com/v5/public/linear"
        self.rest_url = "https://api.bybit.com"
        
        # 实时数据缓存
        self.liquidation_buffer: List[LiquidationEvent] = []
        self.buffer_lock = asyncio.Lock()
        
        # 回调函数
        self.on_liquidation_callback: Optional[Callable] = None
        
    def set_liquidation_callback(self, callback: Callable[[LiquidationEvent], None]):
        """设置清算事件回调函数"""
        self.on_liquidation_callback = callback
        
    async def start_realtime_stream(self):
        """启动实时清算数据流"""
        self.logger.info("启动Bybit实时清算数据流...")
        
        # 构建订阅消息
        subscribe_msg = {
            "op": "subscribe",
            "args": [f"liquidation.{symbol}" for symbol in self.symbols]
        }
        
        try:
            async with websockets.connect(self.ws_url) as websocket:
                # 发送订阅消息
                await websocket.send(json.dumps(subscribe_msg))
                self.logger.info(f"已订阅清算数据: {self.symbols}")
                
                # 处理消息
                async for message in websocket:
                    await self._process_ws_message(message)
                    
        except Exception as e:
            self.logger.error(f"WebSocket连接错误: {e}")
            # 重连逻辑
            await asyncio.sleep(5)
            await self.start_realtime_stream()
            
    async def _process_ws_message(self, message: str):
        """处理WebSocket消息"""
        try:
            data = json.loads(message)
            
            if data.get('topic', '').startswith('liquidation.'):
                liquidation_data = data.get('data', [])
                
                for item in liquidation_data:
                    event = LiquidationEvent(
                        symbol=item['symbol'],
                        side=item['side'],
                        size=float(item['size']),
                        price=float(item['price']),
                        time=datetime.fromtimestamp(int(item['updatedTime']) / 1000),
                        value=float(item['size']) * float(item['price'])
                    )
                    
                    # 添加到缓存
                    async with self.buffer_lock:
                        self.liquidation_buffer.append(event)
                        
                    # 触发回调
                    if self.on_liquidation_callback:
                        self.on_liquidation_callback(event)
                        
                    self.logger.debug(f"清算事件: {event.symbol} {event.side} {event.size}@{event.price}")
                    
        except Exception as e:
            self.logger.error(f"处理WebSocket消息错误: {e}")
            
    def get_historical_liquidations(self, symbol: str, start_time: datetime, 
                                  end_time: datetime) -> pd.DataFrame:
        """
        获取历史清算数据
        
        Args:
            symbol: 交易对
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            包含清算数据的DataFrame
        """
        self.logger.info(f"获取历史清算数据: {symbol} {start_time} - {end_time}")
        
        # 检查本地缓存
        cached_data = self._load_cached_liquidations(symbol, start_time, end_time)
        if not cached_data.empty:
            return cached_data
            
        # 从API获取数据
        liquidations = []
        current_time = start_time
        
        while current_time < end_time:
            batch_end = min(current_time + timedelta(hours=24), end_time)
            batch_data = self._fetch_liquidations_batch(symbol, current_time, batch_end)
            liquidations.extend(batch_data)
            current_time = batch_end
            
        # 转换为DataFrame
        if liquidations:
            df = pd.DataFrame([
                {
                    'timestamp': event.time,
                    'symbol': event.symbol,
                    'side': event.side,
                    'size': event.size,
                    'price': event.price,
                    'value': event.value
                }
                for event in liquidations
            ])
            df.set_index('timestamp', inplace=True)
            
            # 缓存数据
            self._cache_liquidations(df, symbol)
            return df
        else:
            return pd.DataFrame()
            
    def _fetch_liquidations_batch(self, symbol: str, start_time: datetime, 
                                end_time: datetime) -> List[LiquidationEvent]:
        """获取单批次清算数据"""
        # 注意：这里需要实际的API实现
        # 由于Bybit可能没有直接的历史清算API，这里提供框架
        self.logger.warning("历史清算数据API需要实际实现")
        return []
        
    def _load_cached_liquidations(self, symbol: str, start_time: datetime, 
                                end_time: datetime) -> pd.DataFrame:
        """加载缓存的清算数据"""
        cache_file = self.storage_dir / f"{symbol}_liquidations.csv.gz"
        if cache_file.exists():
            try:
                df = pd.read_csv(cache_file, index_col=0, parse_dates=True)
                # 筛选时间范围
                mask = (df.index >= start_time) & (df.index <= end_time)
                return df[mask]
            except Exception as e:
                self.logger.error(f"加载缓存数据错误: {e}")
        return pd.DataFrame()
        
    def _cache_liquidations(self, df: pd.DataFrame, symbol: str):
        """缓存清算数据"""
        cache_file = self.storage_dir / f"{symbol}_liquidations.csv.gz"
        try:
            df.to_csv(cache_file, compression='gzip')
            self.logger.info(f"清算数据已缓存: {cache_file}")
        except Exception as e:
            self.logger.error(f"缓存数据错误: {e}")
            
    def aggregate_liquidations(self, df: pd.DataFrame, timeframe: str = '1min') -> pd.DataFrame:
        """
        聚合清算数据到指定时间框架
        
        Args:
            df: 原始清算数据
            timeframe: 时间框架 ('1min', '5min', '15min', '1h')
            
        Returns:
            聚合后的清算数据
        """
        if df.empty:
            return pd.DataFrame()
            
        # 按时间框架重采样
        agg_dict = {
            'size': 'sum',
            'value': 'sum',
            'price': 'mean'
        }
        
        # 分别处理买入和卖出清算
        buy_liquidations = df[df['side'] == 'Buy'].resample(timeframe).agg(agg_dict)
        sell_liquidations = df[df['side'] == 'Sell'].resample(timeframe).agg(agg_dict)
        
        # 合并结果
        result = pd.DataFrame(index=buy_liquidations.index.union(sell_liquidations.index))
        result['liquidation_buy_volume'] = buy_liquidations['size'].fillna(0)
        result['liquidation_sell_volume'] = sell_liquidations['size'].fillna(0)
        result['liquidation_buy_value'] = buy_liquidations['value'].fillna(0)
        result['liquidation_sell_value'] = sell_liquidations['value'].fillna(0)
        result['liquidation_total_volume'] = result['liquidation_buy_volume'] + result['liquidation_sell_volume']
        result['liquidation_total_value'] = result['liquidation_buy_value'] + result['liquidation_sell_value']
        
        return result.fillna(0)
        
    async def get_current_buffer(self) -> List[LiquidationEvent]:
        """获取当前缓存的清算事件"""
        async with self.buffer_lock:
            buffer_copy = self.liquidation_buffer.copy()
            self.liquidation_buffer.clear()
            return buffer_copy
            
    def stop_stream(self):
        """停止数据流"""
        self.logger.info("停止清算数据流")
        # 实际实现中需要处理WebSocket连接的关闭

    # 实现DataSource抽象方法
    def fetch_data(self, symbol: str, start_time: datetime = None,
                  end_time: datetime = None, **kwargs) -> pd.DataFrame:
        """获取数据的统一接口"""
        return self.get_historical_liquidations(symbol, start_time, end_time)

    def get_symbols(self) -> List[str]:
        """获取支持的交易对列表"""
        return self.symbols

    def get_timeframes(self) -> List[str]:
        """获取支持的时间框架列表"""
        return ['1m', '5m', '15m', '1h']
