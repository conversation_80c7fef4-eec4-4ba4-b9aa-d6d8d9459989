#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
订单流数据提供者

基于FreqTrade原生订单流功能，提供增强的订单流数据处理和分析能力。
集成CVD、OFI、吸收事件检测等高级订单流指标。
"""

import pandas as pd
import numpy as np
import logging
import requests
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Optional, Any, List, Tuple
from freqtrade.data.dataprovider import DataProvider
from freqtrade.data.history import load_pair_history
from freqtrade.configuration import Configuration
from freqtrade.enums import CandleType


class OrderFlowDataProvider:
    """
    订单流数据提供者
    
    基于FreqTrade原生订单流功能，提供：
    1. 订单流数据预处理和缓存
    2. CVD（累积成交量差）计算
    3. OFI（订单流失衡）计算
    4. 吸收事件检测
    5. 币安多空比数据集成
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化订单流数据提供者
        
        Args:
            config: FreqTrade配置字典
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 缓存设置
        self._orderflow_cache: Dict[str, pd.DataFrame] = {}
        self._long_short_ratio_cache: Dict[str, float] = {}
        self._last_ratio_update: Optional[datetime] = None
        
        # 从配置加载参数
        self.orderflow_params = config.get('orderflow_strategy_params', {})
        
        self.logger.info("订单流数据提供者初始化完成")
    
    def enhance_dataframe_with_orderflow(self, dataframe: pd.DataFrame, 
                                       metadata: dict) -> pd.DataFrame:
        """
        使用订单流数据增强标准OHLCV数据框
        
        Args:
            dataframe: 标准OHLCV数据框
            metadata: 交易对元数据
            
        Returns:
            增强后的数据框，包含订单流指标
        """
        pair = metadata['pair']
        
        try:
            # 检查是否有原生订单流数据
            if 'orderflow' not in dataframe.columns:
                self.logger.warning(f"未找到订单流数据 {pair}，请确保配置了use_public_trades: true")
                return self._add_empty_orderflow_indicators(dataframe)
            
            # 计算订单流指标
            dataframe = self._calculate_cvd(dataframe)
            dataframe = self._calculate_ofi(dataframe)
            dataframe = self._detect_absorption_events(dataframe)
            dataframe = self._calculate_orderflow_momentum(dataframe)
            
            # 添加多空比数据
            dataframe = self._add_long_short_ratio(dataframe, pair)
            
            self.logger.debug(f"订单流指标计算完成 {pair}")
            return dataframe
            
        except Exception as e:
            self.logger.error(f"订单流数据处理失败 {pair}: {e}")
            return self._add_empty_orderflow_indicators(dataframe)
    
    def _add_empty_orderflow_indicators(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        添加空的订单流指标（当没有订单流数据时）
        """
        dataframe['cvd'] = 0
        dataframe['cvd_ma'] = 0
        dataframe['cvd_divergence'] = 0
        dataframe['ofi'] = 0
        dataframe['ofi_ma'] = 0
        dataframe['absorption_event'] = False
        dataframe['orderflow_momentum'] = 0
        dataframe['long_short_ratio'] = 1.0
        return dataframe
    
    def _calculate_cvd(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        计算累积成交量差（CVD）
        """
        cvd_values = []
        cumulative_delta = 0
        
        for idx, row in dataframe.iterrows():
            delta = 0
            
            # 优先使用原生delta列
            if 'delta' in dataframe.columns and not pd.isna(row['delta']):
                delta = row['delta']
            elif not pd.isna(row['orderflow']) and row['orderflow']:
                # 从订单流数据计算delta
                delta = self._extract_delta_from_orderflow(row['orderflow'])
            
            cumulative_delta += delta
            cvd_values.append(cumulative_delta)
        
        dataframe['cvd'] = cvd_values
        
        # CVD移动平均
        cvd_period = self.orderflow_params.get('cvd_period', 20)
        dataframe['cvd_ma'] = dataframe['cvd'].rolling(cvd_period).mean()
        
        # CVD背离检测
        dataframe['cvd_divergence'] = self._detect_cvd_divergence(dataframe)
        
        return dataframe
    
    def _extract_delta_from_orderflow(self, orderflow_data: Any) -> float:
        """
        从订单流数据提取delta值
        """
        if not isinstance(orderflow_data, dict):
            return 0
        
        total_delta = 0
        for price_level, data in orderflow_data.items():
            if isinstance(data, dict):
                bid_amount = data.get('bid_amount', 0)
                ask_amount = data.get('ask_amount', 0)
                total_delta += bid_amount - ask_amount
        
        return total_delta
    
    def _detect_cvd_divergence(self, dataframe: pd.DataFrame) -> pd.Series:
        """
        检测CVD背离
        """
        period = self.orderflow_params.get('cvd_period', 20)
        threshold = self.orderflow_params.get('cvd_divergence_threshold', 0.7)
        
        # 计算价格和CVD的变化率
        price_change = dataframe['close'].pct_change(period)
        cvd_change = dataframe['cvd'].pct_change(period)
        
        # 背离检测
        divergence = np.where(
            (price_change > threshold) & (cvd_change < -threshold),
            -1,  # 看跌背离
            np.where(
                (price_change < -threshold) & (cvd_change > threshold),
                1,   # 看涨背离
                0    # 无背离
            )
        )
        
        return pd.Series(divergence, index=dataframe.index)
    
    def _calculate_ofi(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        计算订单流失衡（OFI）
        """
        ofi_values = []
        
        for idx, row in dataframe.iterrows():
            ofi = 0
            
            if not pd.isna(row['orderflow']) and row['orderflow']:
                total_bid_flow = 0
                total_ask_flow = 0
                
                orderflow_data = row['orderflow']
                if isinstance(orderflow_data, dict):
                    for price_level, data in orderflow_data.items():
                        if isinstance(data, dict):
                            total_bid_flow += data.get('bid_amount', 0)
                            total_ask_flow += data.get('ask_amount', 0)
                
                # OFI计算
                total_flow = total_bid_flow + total_ask_flow
                if total_flow > 0:
                    ofi = (total_bid_flow - total_ask_flow) / total_flow
            
            ofi_values.append(ofi)
        
        dataframe['ofi'] = ofi_values
        
        # OFI移动平均
        ofi_period = self.orderflow_params.get('ofi_period', 10)
        dataframe['ofi_ma'] = dataframe['ofi'].rolling(ofi_period).mean()
        
        return dataframe
    
    def _detect_absorption_events(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        检测吸收事件
        """
        absorption_events = []
        volume_threshold = self.orderflow_params.get('absorption_volume_threshold', 3.0)
        price_tolerance = self.orderflow_params.get('absorption_price_tolerance', 0.0005)
        
        for idx, row in dataframe.iterrows():
            absorption = False
            
            if not pd.isna(row['orderflow']) and row['orderflow']:
                orderflow_data = row['orderflow']
                if isinstance(orderflow_data, dict):
                    # 计算总成交量
                    total_orderflow_volume = sum(
                        data.get('total_volume', 0) 
                        for data in orderflow_data.values() 
                        if isinstance(data, dict)
                    )
                    
                    # 检查价格变动
                    if idx > 0:
                        price_change = abs(row['close'] - dataframe.iloc[idx-1]['close']) / dataframe.iloc[idx-1]['close']
                        
                        # 吸收事件：大成交量但小价格变动
                        if (total_orderflow_volume > volume_threshold * row['volume'] and 
                            price_change < price_tolerance):
                            absorption = True
            
            absorption_events.append(absorption)
        
        dataframe['absorption_event'] = absorption_events
        return dataframe
    
    def _calculate_orderflow_momentum(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        计算订单流动量
        """
        # 基于CVD和OFI的综合动量指标
        dataframe['orderflow_momentum'] = (
            dataframe['cvd'].pct_change(5) * 0.6 +
            dataframe['ofi'].rolling(5).mean() * 0.4
        )
        
        return dataframe
    
    def _add_long_short_ratio(self, dataframe: pd.DataFrame, pair: str) -> pd.DataFrame:
        """
        添加币安多空比数据
        """
        try:
            ratio = self._get_long_short_ratio(pair)
            dataframe['long_short_ratio'] = ratio
        except Exception as e:
            self.logger.warning(f"获取多空比失败 {pair}: {e}")
            dataframe['long_short_ratio'] = 1.0
        
        return dataframe
    
    def _get_long_short_ratio(self, pair: str) -> float:
        """
        获取币安多空比数据
        """
        try:
            # 缓存机制：每5分钟更新一次
            now = datetime.now()
            if (self._last_ratio_update is None or 
                (now - self._last_ratio_update).total_seconds() > 300):
                
                # 转换交易对格式
                symbol = pair.replace('/USDC:USDC', 'USDC').replace('/', '')
                
                # 调用币安API
                url = "https://fapi.binance.com/futures/data/globalLongShortAccountRatio"
                params = {
                    'symbol': symbol,
                    'period': '5m',
                    'limit': 1
                }
                
                response = requests.get(url, params=params, timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    if data:
                        ratio = float(data[0]['longShortRatio'])
                        self._long_short_ratio_cache[pair] = ratio
                        self._last_ratio_update = now
                        return ratio
            
            return self._long_short_ratio_cache.get(pair, 1.0)
            
        except Exception as e:
            self.logger.warning(f"获取多空比API调用失败 {pair}: {e}")
            return 1.0
    
    def get_orderflow_summary(self, pair: str) -> Dict[str, Any]:
        """
        获取订单流数据摘要
        """
        try:
            if pair in self._orderflow_cache:
                df = self._orderflow_cache[pair]
                if not df.empty:
                    latest = df.iloc[-1]
                    return {
                        'pair': pair,
                        'cvd': latest.get('cvd', 0),
                        'cvd_divergence': latest.get('cvd_divergence', 0),
                        'ofi': latest.get('ofi', 0),
                        'absorption_event': latest.get('absorption_event', False),
                        'orderflow_momentum': latest.get('orderflow_momentum', 0),
                        'long_short_ratio': latest.get('long_short_ratio', 1.0),
                        'timestamp': latest.name if hasattr(latest, 'name') else datetime.now()
                    }
        except Exception as e:
            self.logger.error(f"获取订单流摘要失败 {pair}: {e}")
        
        return {'pair': pair, 'error': 'No data available'}
