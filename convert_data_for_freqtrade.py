#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据转换脚本 - 将现有数据转换为FreqTrade格式

将 data/storage/data/BTC_USDT/5m.csv.gz 转换为 FreqTrade 期望的格式
"""

import os
import json
import pandas as pd
import gzip
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def convert_to_freqtrade_format():
    """转换数据为FreqTrade格式"""
    logger.info("🔄 开始转换数据为FreqTrade格式")
    
    # 源数据路径
    source_file = "data/storage/data/BTC_USDT/5m.csv.gz"
    
    # 目标路径
    target_dir = Path("user_data/data/binance/futures")
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # FreqTrade期望的文件名格式 - 修正格式
    target_file = target_dir / "BTC_USDT-5m.json"
    
    try:
        # 读取源数据
        logger.info(f"📖 读取源数据: {source_file}")
        with gzip.open(source_file, 'rt') as f:
            data = pd.read_csv(f, index_col=0, parse_dates=True)
        
        logger.info(f"✅ 数据加载成功: {len(data)} 行")
        logger.info(f"📅 时间范围: {data.index.min()} - {data.index.max()}")
        logger.info(f"📊 数据列: {list(data.columns)}")
        
        # 检查数据格式
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            logger.error(f"❌ 缺少必要列: {missing_columns}")
            return False
        
        # 转换为FreqTrade格式
        logger.info("🔄 转换数据格式...")
        
        # FreqTrade期望的数据格式：
        # [[timestamp_ms, open, high, low, close, volume], ...]
        freqtrade_data = []
        
        for timestamp, row in data.iterrows():
            # 转换时间戳为毫秒
            timestamp_ms = int(timestamp.timestamp() * 1000)
            
            freqtrade_data.append([
                timestamp_ms,
                float(row['open']),
                float(row['high']),
                float(row['low']),
                float(row['close']),
                float(row['volume'])
            ])
        
        # 保存为JSON格式
        logger.info(f"💾 保存到: {target_file}")
        with open(target_file, 'w') as f:
            json.dump(freqtrade_data, f)
        
        logger.info(f"✅ 数据转换完成: {len(freqtrade_data)} 条记录")
        
        # 验证转换结果
        verify_converted_data(target_file)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据转换失败: {e}")
        return False


def verify_converted_data(file_path: str):
    """验证转换后的数据"""
    logger.info("🔍 验证转换后的数据...")
    
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        if not data:
            logger.error("❌ 转换后的数据为空")
            return False
        
        # 检查第一条和最后一条记录
        first_record = data[0]
        last_record = data[-1]
        
        logger.info(f"📊 数据验证:")
        logger.info(f"  总记录数: {len(data)}")
        logger.info(f"  第一条记录: {first_record}")
        logger.info(f"  最后一条记录: {last_record}")
        
        # 验证数据结构
        if len(first_record) != 6:
            logger.error(f"❌ 数据结构错误，期望6列，实际{len(first_record)}列")
            return False
        
        # 验证时间戳
        from datetime import datetime
        first_time = datetime.fromtimestamp(first_record[0] / 1000)
        last_time = datetime.fromtimestamp(last_record[0] / 1000)
        
        logger.info(f"  时间范围: {first_time} - {last_time}")
        logger.info("✅ 数据验证通过")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据验证失败: {e}")
        return False


def create_freqtrade_data_structure():
    """创建FreqTrade期望的目录结构"""
    logger.info("📁 创建FreqTrade数据目录结构...")
    
    # 创建必要的目录
    directories = [
        "user_data/data/binance/futures",
        "user_data/data/binance/spot",
        "user_data/backtest_results",
        "user_data/hyperopt_results"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ 创建目录: {directory}")


def main():
    """主函数"""
    logger.info("🚀 FreqTrade数据转换工具")
    
    # 检查源数据是否存在
    source_file = "data/storage/data/BTC_USDT/5m.csv.gz"
    if not os.path.exists(source_file):
        logger.error(f"❌ 源数据文件不存在: {source_file}")
        return 1
    
    # 创建目录结构
    create_freqtrade_data_structure()
    
    # 转换数据
    if convert_to_freqtrade_format():
        logger.info("🎉 数据转换成功！")
        logger.info("💡 现在可以运行FreqTrade回测了")
        return 0
    else:
        logger.error("❌ 数据转换失败")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
