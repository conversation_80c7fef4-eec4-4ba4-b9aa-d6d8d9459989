#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
逆向资金费率策略数据下载器

基于逆向资金费率策略文档，实现资金费率和持仓量数据的批量下载。
支持历史数据获取和freqtrade格式转换，为策略回测提供数据基础。

功能特点：
1. 批量下载6个主流交易对的资金费率历史数据
2. 获取对应的持仓量(Open Interest)数据
3. 时间对齐和数据质量验证
4. 转换为freqtrade兼容的parquet格式存储
"""

import requests
import pandas as pd
import numpy as np
import time
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import ccxt

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FundingRateDataDownloader:
    """
    资金费率数据下载器
    
    基于币安期货API实现资金费率和持仓量数据的批量下载
    确保数据质量和时间对齐，为逆向策略提供可靠数据源
    """
    
    def __init__(self, output_dir: str = "user_data/data/funding_rates"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化币安交易所连接
        self.exchange = ccxt.binance({
            'apiKey': '',  # 只读API，用于获取公开数据
            'secret': '',
            'sandbox': False,
            'options': {'defaultType': 'future'}
        })
        
        # 目标交易对列表（freqtrade格式 -> 币安格式）
        self.target_pairs = {
            'BTC/USDT:USDT': 'BTCUSDT',
            'ETH/USDT:USDT': 'ETHUSDT', 
            'SOL/USDT:USDT': 'SOLUSDT',
            'BNB/USDT:USDT': 'BNBUSDT',
            'XRP/USDT:USDT': 'XRPUSDT',
            'ADA/USDT:USDT': 'ADAUSDT'
        }
        
        # 资金费率结算周期（8小时 = 28800秒）
        self.funding_interval = 8 * 60 * 60 * 1000  # 毫秒
        
    def download_funding_rate_history(self, symbol: str, 
                                    start_date: str = "2024-07-01",
                                    end_date: str = "2025-07-01",
                                    limit: int = 1000) -> Optional[pd.DataFrame]:
        """
        下载历史资金费率数据
        
        Args:
            symbol: 币安交易对符号（如 'BTCUSDT'）
            start_date: 开始日期 "YYYY-MM-DD"
            end_date: 结束日期 "YYYY-MM-DD"
            limit: 每次API调用的数据条数限制
            
        Returns:
            资金费率历史数据DataFrame
        """
        logger.info(f"🔄 下载 {symbol} 资金费率数据: {start_date} 到 {end_date}")
        
        start_timestamp = int(datetime.strptime(start_date, "%Y-%m-%d").timestamp() * 1000)
        end_timestamp = int(datetime.strptime(end_date, "%Y-%m-%d").timestamp() * 1000)
        
        all_funding_data = []
        current_start = start_timestamp
        
        while current_start < end_timestamp:
            try:
                # 使用ccxt获取资金费率历史
                funding_rates = self.exchange.fetch_funding_rate_history(
                    symbol=symbol,
                    since=current_start,
                    limit=limit
                )
                
                if not funding_rates:
                    logger.warning(f"⚠️ 未获取到数据: {symbol} {current_start}")
                    break
                    
                all_funding_data.extend(funding_rates)
                logger.info(f"✅ 获取 {len(funding_rates)} 条资金费率记录")
                
                # 更新下次起始时间
                if funding_rates:
                    last_timestamp = funding_rates[-1]['timestamp']
                    current_start = last_timestamp + 1
                else:
                    break
                    
                # API限制保护
                time.sleep(0.2)
                
            except Exception as e:
                logger.error(f"❌ 下载资金费率失败 {symbol}: {e}")
                break
                
        if not all_funding_data:
            logger.error(f"❌ 未获取到任何资金费率数据: {symbol}")
            return None
            
        # 转换为DataFrame
        df_funding = pd.DataFrame(all_funding_data)
        df_funding['timestamp'] = pd.to_datetime(df_funding['timestamp'], unit='ms')
        df_funding = df_funding.sort_values('timestamp').reset_index(drop=True)
        
        logger.info(f"✅ 资金费率数据转换完成: {len(df_funding)} 条记录")
        return df_funding
        
    def download_open_interest_history(self, symbol: str,
                                     start_date: str = "2024-07-01", 
                                     end_date: str = "2025-07-01") -> Optional[pd.DataFrame]:
        """
        下载历史持仓量数据
        
        Args:
            symbol: 币安交易对符号
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            持仓量历史数据DataFrame
        """
        logger.info(f"🔄 下载 {symbol} 持仓量数据")
        
        # 币安持仓量API端点
        base_url = "https://fapi.binance.com"
        endpoint = "/fapi/v1/openInterestHist"
        
        start_timestamp = int(datetime.strptime(start_date, "%Y-%m-%d").timestamp() * 1000)
        end_timestamp = int(datetime.strptime(end_date, "%Y-%m-%d").timestamp() * 1000)
        
        all_oi_data = []
        current_start = start_timestamp
        
        while current_start < end_timestamp:
            # 每次请求30天的数据（API限制）
            current_end = min(current_start + (30 * 24 * 60 * 60 * 1000), end_timestamp)
            
            params = {
                'symbol': symbol,
                'period': '5m',  # 5分钟粒度
                'startTime': current_start,
                'endTime': current_end,
                'limit': 500
            }
            
            try:
                response = requests.get(base_url + endpoint, params=params, timeout=30)
                response.raise_for_status()
                data = response.json()
                
                if data:
                    all_oi_data.extend(data)
                    logger.info(f"✅ 获取 {len(data)} 条持仓量记录")
                
                current_start = current_end + 1
                time.sleep(0.2)  # API限制保护
                
            except Exception as e:
                logger.error(f"❌ 下载持仓量失败 {symbol}: {e}")
                break
                
        if not all_oi_data:
            logger.warning(f"⚠️ 未获取到持仓量数据: {symbol}")
            return None
            
        # 转换为DataFrame
        df_oi = pd.DataFrame(all_oi_data)
        df_oi['timestamp'] = pd.to_datetime(df_oi['timestamp'], unit='ms')
        df_oi['openInterest'] = pd.to_numeric(df_oi['sumOpenInterest'])
        df_oi = df_oi[['timestamp', 'openInterest']].sort_values('timestamp').reset_index(drop=True)
        
        logger.info(f"✅ 持仓量数据转换完成: {len(df_oi)} 条记录")
        return df_oi
        
    def merge_funding_and_oi_data(self, df_funding: pd.DataFrame, 
                                 df_oi: pd.DataFrame) -> pd.DataFrame:
        """
        合并资金费率和持仓量数据
        
        Args:
            df_funding: 资金费率数据
            df_oi: 持仓量数据
            
        Returns:
            合并后的完整数据集
        """
        logger.info("🔄 合并资金费率和持仓量数据")
        
        if df_funding is None or df_funding.empty:
            logger.error("❌ 资金费率数据为空，无法合并")
            return pd.DataFrame()
            
        # 准备资金费率数据
        df_funding_clean = df_funding[['timestamp', 'fundingRate']].copy()
        df_funding_clean.columns = ['timestamp', 'funding_rate']
        
        # 如果没有持仓量数据，创建空列
        if df_oi is None or df_oi.empty:
            logger.warning("⚠️ 持仓量数据为空，使用NaN填充")
            df_funding_clean['open_interest'] = np.nan
            merged_df = df_funding_clean
        else:
            # 使用pandas merge_asof进行时间对齐合并
            merged_df = pd.merge_asof(
                df_funding_clean.sort_values('timestamp'),
                df_oi.sort_values('timestamp'),
                on='timestamp',
                direction='backward'  # 避免未来数据泄露
            )
            
        # 添加计算字段
        merged_df['funding_rate_pct'] = merged_df['funding_rate'] * 100  # 转换为百分比
        merged_df['funding_rate_ma5'] = merged_df['funding_rate'].rolling(5).mean()
        merged_df['funding_rate_ma20'] = merged_df['funding_rate'].rolling(20).mean()
        
        # 持仓量变化率
        if 'open_interest' in merged_df.columns:
            merged_df['oi_change_pct'] = merged_df['open_interest'].pct_change()
            merged_df['oi_ma20'] = merged_df['open_interest'].rolling(20).mean()
        
        # 资金费率分类标签（基于策略文档阈值）
        merged_df['funding_category'] = pd.cut(
            merged_df['funding_rate'],
            bins=[-np.inf, -0.00075, -0.0003, 0.0003, 0.00075, np.inf],
            labels=['extreme_negative', 'negative', 'neutral', 'positive', 'extreme_positive']
        )
        
        logger.info(f"✅ 数据合并完成: {len(merged_df)} 条记录")
        return merged_df
        
    def save_to_parquet(self, df: pd.DataFrame, pair: str) -> bool:
        """
        保存数据为parquet格式
        
        Args:
            df: 要保存的数据
            pair: freqtrade格式的交易对名称
            
        Returns:
            保存成功标志
        """
        if df.empty:
            logger.error("❌ 数据为空，无法保存")
            return False
            
        # 生成文件名
        pair_clean = pair.replace('/', '_').replace(':', '_')
        filename = f"{pair_clean}_funding_rate_1m.parquet"
        filepath = self.output_dir / filename
        
        try:
            # 设置时间戳为索引
            df_save = df.copy()
            df_save.set_index('timestamp', inplace=True)
            
            # 保存为parquet格式
            df_save.to_parquet(filepath, compression='snappy')
            
            logger.info(f"💾 数据保存成功: {filepath}")
            logger.info(f"📊 数据统计: {len(df_save)} 条记录")
            logger.info(f"📅 时间范围: {df_save.index.min()} 到 {df_save.index.max()}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存失败: {e}")
            return False
            
    def download_all_pairs(self, start_date: str = "2024-07-01", 
                          end_date: str = "2025-07-01") -> Dict[str, bool]:
        """
        批量下载所有目标交易对的数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            各交易对下载结果状态
        """
        logger.info("🚀 开始批量下载资金费率和持仓量数据")
        logger.info(f"📅 时间范围: {start_date} 到 {end_date}")
        logger.info(f"📊 目标交易对: {list(self.target_pairs.keys())}")
        
        results = {}
        
        for freqtrade_pair, binance_symbol in self.target_pairs.items():
            logger.info(f"\n🔄 处理 {freqtrade_pair} (币安: {binance_symbol})")
            
            try:
                # 下载资金费率数据
                df_funding = self.download_funding_rate_history(
                    binance_symbol, start_date, end_date
                )
                
                # 下载持仓量数据
                df_oi = self.download_open_interest_history(
                    binance_symbol, start_date, end_date  
                )
                
                # 合并数据
                merged_data = self.merge_funding_and_oi_data(df_funding, df_oi)
                
                # 保存数据
                if not merged_data.empty:
                    success = self.save_to_parquet(merged_data, freqtrade_pair)
                    results[freqtrade_pair] = success
                else:
                    logger.error(f"❌ {freqtrade_pair} 数据合并失败")
                    results[freqtrade_pair] = False
                    
            except Exception as e:
                logger.error(f"❌ {freqtrade_pair} 处理失败: {e}")
                results[freqtrade_pair] = False
                
            # 避免API限制
            time.sleep(1)
            
        # 统计结果
        success_count = sum(results.values())
        total_count = len(results)
        
        logger.info(f"\n🎉 批量下载完成!")
        logger.info(f"📊 成功: {success_count}/{total_count} 个交易对")
        
        for pair, status in results.items():
            status_emoji = "✅" if status else "❌"
            logger.info(f"{status_emoji} {pair}")
            
        return results

def main():
    """主函数：执行数据下载任务"""
    logger.info("🚀 启动逆向资金费率策略数据下载器")
    
    # 创建下载器实例
    downloader = FundingRateDataDownloader()
    
    # 执行批量下载
    results = downloader.download_all_pairs(
        start_date="2024-07-01",
        end_date="2025-07-01"
    )
    
    # 输出最终结果
    success_pairs = [pair for pair, status in results.items() if status]
    failed_pairs = [pair for pair, status in results.items() if not status]
    
    if success_pairs:
        logger.info(f"✅ 成功下载的交易对: {success_pairs}")
    if failed_pairs:
        logger.info(f"❌ 下载失败的交易对: {failed_pairs}")
        
    logger.info("🎉 数据下载任务完成!")

if __name__ == "__main__":
    main() 