#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
瀑布猎人策略数据预处理器

实现WaterFallAlpha策略所需的多源数据整合和预处理，包括：
1. OHLCV数据与清算数据的时间对齐
2. 订单簿数据的OBI计算
3. 微观结构指标的计算
4. 避免未来函数的数据合并
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np
from pathlib import Path

from data.sources.bybit_liquidation import BybitLiquidationSource
from data.sources.orderbook_source import OrderBookSource
from indicators.microstructure_indicators import MicrostructureAnalyzer
from data.storage.optimized_storage import OptimizedStorage


class WaterfallDataPreprocessor:
    """
    瀑布猎人策略数据预处理器
    
    负责整合多源数据，生成策略所需的统一数据集，确保：
    1. 时间对齐的准确性
    2. 避免未来函数
    3. 数据质量和完整性
    """
    
    def __init__(self, storage_dir: str = None):
        """
        初始化数据预处理器
        
        Args:
            storage_dir: 数据存储目录
        """
        self.storage_dir = Path(storage_dir) if storage_dir else Path('data/storage/waterfall')
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化数据源
        self.liquidation_source = BybitLiquidationSource(
            storage_dir=str(self.storage_dir / 'liquidations')
        )
        self.orderbook_source = OrderBookSource(
            storage_dir=str(self.storage_dir / 'orderbook')
        )
        
        # 初始化微观结构分析器
        self.microstructure_analyzer = MicrostructureAnalyzer()
        
        # 数据存储
        self.storage = OptimizedStorage(str(self.storage_dir / 'processed'))
        
    def prepare_strategy_data(self, symbol: str, timeframe: str, 
                            start_time: datetime, end_time: datetime,
                            base_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        准备策略所需的完整数据集
        
        Args:
            symbol: 交易对
            timeframe: 时间框架 ('1m', '5m')
            start_time: 开始时间
            end_time: 结束时间
            base_data: 基础OHLCV数据，如果为None则自动加载
            
        Returns:
            包含所有策略指标的DataFrame
        """
        self.logger.info(f"准备瀑布策略数据: {symbol} {timeframe} {start_time} - {end_time}")
        
        # 1. 获取基础OHLCV数据
        if base_data is None:
            base_data = self._load_base_data(symbol, timeframe, start_time, end_time)
            
        if base_data.empty:
            self.logger.error(f"无法获取基础数据: {symbol}")
            return pd.DataFrame()
            
        # 2. 获取清算数据
        liquidation_data = self._prepare_liquidation_data(symbol, timeframe, start_time, end_time)
        
        # 3. 获取订单簿数据 (模拟数据，实际需要历史订单簿)
        orderbook_data = self._prepare_orderbook_data(symbol, timeframe, start_time, end_time)
        
        # 4. 计算微观结构指标
        microstructure_data = self._calculate_microstructure_indicators(base_data)
        
        # 5. 合并所有数据 (使用merge_asof避免未来函数)
        final_data = self._merge_all_data(
            base_data, liquidation_data, orderbook_data, microstructure_data
        )
        
        # 6. 计算策略特征
        final_data = self._calculate_strategy_features(final_data)
        
        # 7. 缓存处理后的数据
        self._cache_processed_data(final_data, symbol, timeframe)
        
        self.logger.info(f"数据准备完成: {len(final_data)} 行数据")
        return final_data
        
    def _load_base_data(self, symbol: str, timeframe: str, 
                       start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """加载基础OHLCV数据"""
        try:
            # 尝试从存储加载
            data = self.storage.load_data(symbol, timeframe, start_time, end_time)
            if not data.empty:
                return data
                
            # 如果没有数据，返回空DataFrame
            self.logger.warning(f"未找到基础数据: {symbol} {timeframe}")
            return pd.DataFrame()
            
        except Exception as e:
            self.logger.error(f"加载基础数据错误: {e}")
            return pd.DataFrame()
            
    def _prepare_liquidation_data(self, symbol: str, timeframe: str,
                                start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """准备清算数据"""
        try:
            # 获取原始清算数据
            raw_liquidations = self.liquidation_source.get_historical_liquidations(
                symbol, start_time, end_time
            )
            
            if raw_liquidations.empty:
                self.logger.warning(f"未找到清算数据: {symbol}")
                # 返回空的清算数据结构
                return self._create_empty_liquidation_data(start_time, end_time, timeframe)
                
            # 聚合到指定时间框架
            aggregated = self.liquidation_source.aggregate_liquidations(raw_liquidations, timeframe)
            
            # 计算清算量统计指标
            aggregated = self._calculate_liquidation_features(aggregated)
            
            return aggregated
            
        except Exception as e:
            self.logger.error(f"准备清算数据错误: {e}")
            return self._create_empty_liquidation_data(start_time, end_time, timeframe)
            
    def _prepare_orderbook_data(self, symbol: str, timeframe: str,
                              start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """准备订单簿数据"""
        try:
            # 注意：这里需要历史订单簿数据，实际实现中可能需要从其他源获取
            # 目前创建模拟数据结构
            
            time_range = pd.date_range(start=start_time, end=end_time, freq=timeframe)
            
            # 创建模拟OBI数据
            np.random.seed(42)  # 确保可重现性
            obi_data = pd.DataFrame({
                'obi_5': np.random.normal(0, 0.2, len(time_range)),
                'obi_10': np.random.normal(0, 0.15, len(time_range)),
                'bid_volume_5': np.random.exponential(1000, len(time_range)),
                'ask_volume_5': np.random.exponential(1000, len(time_range)),
                'bid_volume_10': np.random.exponential(2000, len(time_range)),
                'ask_volume_10': np.random.exponential(2000, len(time_range))
            }, index=time_range)
            
            # 添加一些现实的模式
            obi_data['obi_5'] = np.clip(obi_data['obi_5'], -0.8, 0.8)
            obi_data['obi_10'] = np.clip(obi_data['obi_10'], -0.6, 0.6)
            
            self.logger.info(f"创建模拟订单簿数据: {len(obi_data)} 行")
            return obi_data
            
        except Exception as e:
            self.logger.error(f"准备订单簿数据错误: {e}")
            return pd.DataFrame()
            
    def _calculate_microstructure_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算微观结构指标"""
        try:
            # 使用微观结构分析器
            microstructure_data = self.microstructure_analyzer.analyze(data)
            
            # 只返回微观结构相关的列
            microstructure_cols = ['vpin', 'roll_measure', 'toxicity_score', 'microstructure_signal']
            return microstructure_data[microstructure_cols]
            
        except Exception as e:
            self.logger.error(f"计算微观结构指标错误: {e}")
            # 返回空的微观结构数据
            return pd.DataFrame(index=data.index)
            
    def _merge_all_data(self, base_data: pd.DataFrame, liquidation_data: pd.DataFrame,
                       orderbook_data: pd.DataFrame, microstructure_data: pd.DataFrame) -> pd.DataFrame:
        """
        合并所有数据源，使用merge_asof避免未来函数
        """
        result = base_data.copy()
        
        # 确保索引是datetime类型
        if not isinstance(result.index, pd.DatetimeIndex):
            result.index = pd.to_datetime(result.index)
            
        # 合并清算数据
        if not liquidation_data.empty:
            result = pd.merge_asof(
                result.sort_index(), 
                liquidation_data.sort_index(),
                left_index=True, 
                right_index=True,
                direction='backward'  # 使用向后查找，避免未来函数
            )
            
        # 合并订单簿数据
        if not orderbook_data.empty:
            result = pd.merge_asof(
                result.sort_index(),
                orderbook_data.sort_index(),
                left_index=True,
                right_index=True,
                direction='backward'
            )
            
        # 合并微观结构数据
        if not microstructure_data.empty:
            result = pd.merge_asof(
                result.sort_index(),
                microstructure_data.sort_index(),
                left_index=True,
                right_index=True,
                direction='backward'
            )
            
        # 填充缺失值
        result = self._fill_missing_values(result)
        
        return result
        
    def _calculate_strategy_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算策略特征"""
        result = data.copy()
        
        # 清算量特征
        if 'liquidation_total_volume' in result.columns:
            # 清算量移动平均
            result['liquidation_ma_60'] = result['liquidation_total_volume'].rolling(60).mean()
            result['liquidation_std_60'] = result['liquidation_total_volume'].rolling(60).std()
            
            # 清算量异常检测
            result['liquidation_zscore'] = (
                (result['liquidation_total_volume'] - result['liquidation_ma_60']) / 
                result['liquidation_std_60']
            )
            
            # 清算量激增信号
            result['liquidation_spike'] = result['liquidation_zscore'] > 3
            
        # OBI特征
        if 'obi_10' in result.columns:
            # OBI移动平均
            result['obi_10_ma'] = result['obi_10'].rolling(20).mean()
            result['obi_10_std'] = result['obi_10'].rolling(20).std()
            
            # OBI极值检测
            result['obi_extreme_negative'] = result['obi_10'] < -0.4
            result['obi_extreme_positive'] = result['obi_10'] > 0.4
            
        # 价格特征
        result['price_change'] = result['close'].pct_change()
        result['price_volatility'] = result['price_change'].rolling(20).std()
        
        # ATR (平均真实波幅)
        result['tr'] = np.maximum(
            result['high'] - result['low'],
            np.maximum(
                abs(result['high'] - result['close'].shift(1)),
                abs(result['low'] - result['close'].shift(1))
            )
        )
        result['atr_14'] = result['tr'].rolling(14).mean()
        
        return result
        
    def _create_empty_liquidation_data(self, start_time: datetime, 
                                     end_time: datetime, timeframe: str) -> pd.DataFrame:
        """创建空的清算数据结构"""
        time_range = pd.date_range(start=start_time, end=end_time, freq=timeframe)
        return pd.DataFrame({
            'liquidation_buy_volume': 0,
            'liquidation_sell_volume': 0,
            'liquidation_buy_value': 0,
            'liquidation_sell_value': 0,
            'liquidation_total_volume': 0,
            'liquidation_total_value': 0
        }, index=time_range)
        
    def _calculate_liquidation_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算清算数据特征"""
        result = data.copy()
        
        # 清算不平衡
        result['liquidation_imbalance'] = (
            result['liquidation_buy_volume'] - result['liquidation_sell_volume']
        ) / (result['liquidation_buy_volume'] + result['liquidation_sell_volume'] + 1e-8)
        
        # 清算强度
        result['liquidation_intensity'] = result['liquidation_total_volume'].rolling(5).sum()
        
        return result
        
    def _fill_missing_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """填充缺失值"""
        # 前向填充
        data = data.fillna(method='ffill')
        
        # 剩余的用0填充
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        data[numeric_columns] = data[numeric_columns].fillna(0)
        
        return data
        
    def _cache_processed_data(self, data: pd.DataFrame, symbol: str, timeframe: str):
        """缓存处理后的数据"""
        try:
            cache_file = self.storage_dir / f"{symbol}_{timeframe}_processed.csv.gz"
            data.to_csv(cache_file, compression='gzip')
            self.logger.info(f"数据已缓存: {cache_file}")
        except Exception as e:
            self.logger.error(f"缓存数据错误: {e}")
            
    def get_cached_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """获取缓存的处理数据"""
        try:
            cache_file = self.storage_dir / f"{symbol}_{timeframe}_processed.csv.gz"
            if cache_file.exists():
                return pd.read_csv(cache_file, index_col=0, parse_dates=True)
        except Exception as e:
            self.logger.error(f"读取缓存数据错误: {e}")
        return None
