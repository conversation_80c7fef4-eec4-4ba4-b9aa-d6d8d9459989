#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单簿数据下载脚本 - 专门下载最近30天的订单簿快照数据
使用Binance官方API获取订单簿深度数据

功能：
- 下载Binance订单簿深度快照
- 计算订单簿不平衡指标
- 分析流动性分布
- 保存为FreqTrade兼容格式
"""

import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import requests
import logging
import time
import numpy as np

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OrderbookDataDownloader:
    """订单簿数据下载器 - 获取市场深度和流动性数据"""
    
    def __init__(self, symbol: str = "BTCUSDT"):
        self.symbol = symbol
        
        # 创建数据目录
        self.data_dir = Path("user_data/data")
        self.orderbook_dir = self.data_dir / "orderbook"
        self.orderbook_dir.mkdir(parents=True, exist_ok=True)
        
        # 订单簿参数
        self.depth_levels = 100  # 获取前100档深度
        self.analysis_levels = 20  # 分析前20档
        self.snapshot_interval_minutes = 15  # 每15分钟获取一次快照
        
        logger.info(f"订单簿数据下载器初始化完成，目标交易对: {self.symbol}")

    def get_orderbook_snapshot(self) -> dict:
        """
        获取单次订单簿快照
        """
        base_url = "https://api.binance.com/api/v3"
        
        try:
            url = f"{base_url}/depth"
            params = {
                'symbol': self.symbol,
                'limit': self.depth_levels
            }
            
            response = requests.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return data
            elif response.status_code == 429:
                logger.warning("API限制，等待60秒...")
                time.sleep(60)
                return None
            else:
                logger.error(f"获取订单簿失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取订单簿快照时出错: {e}")
            return None

    def analyze_orderbook(self, orderbook_data: dict, timestamp: datetime) -> dict:
        """
        分析订单簿数据计算各种指标
        """
        if not orderbook_data or 'bids' not in orderbook_data or 'asks' not in orderbook_data:
            return None
        
        bids = orderbook_data['bids'][:self.analysis_levels]
        asks = orderbook_data['asks'][:self.analysis_levels]
        
        if not bids or not asks:
            return None
        
        # 转换为数值
        bid_prices = [float(bid[0]) for bid in bids]
        bid_quantities = [float(bid[1]) for bid in bids]
        ask_prices = [float(ask[0]) for ask in asks]
        ask_quantities = [float(ask[1]) for ask in asks]
        
        # 基础指标
        best_bid = bid_prices[0]
        best_ask = ask_prices[0]
        spread = best_ask - best_bid
        spread_pct = (spread / best_bid) * 100
        
        # 成交量指标
        bid_volume_5 = sum(bid_quantities[:5])
        ask_volume_5 = sum(ask_quantities[:5])
        bid_volume_10 = sum(bid_quantities[:10])
        ask_volume_10 = sum(ask_quantities[:10])
        bid_volume_20 = sum(bid_quantities[:20])
        ask_volume_20 = sum(ask_quantities[:20])
        
        # 订单簿不平衡
        total_volume_5 = bid_volume_5 + ask_volume_5
        total_volume_10 = bid_volume_10 + ask_volume_10
        total_volume_20 = bid_volume_20 + ask_volume_20
        
        imbalance_5 = (bid_volume_5 - ask_volume_5) / total_volume_5 if total_volume_5 > 0 else 0
        imbalance_10 = (bid_volume_10 - ask_volume_10) / total_volume_10 if total_volume_10 > 0 else 0
        imbalance_20 = (bid_volume_20 - ask_volume_20) / total_volume_20 if total_volume_20 > 0 else 0
        
        # 流动性指标
        liquidity_score = min(100, total_volume_10 * 10)
        
        # 价格影响分析
        # 计算买入/卖出1 BTC对价格的影响
        cumulative_bid_qty = 0
        cumulative_ask_qty = 0
        price_impact_buy = 0
        price_impact_sell = 0
        
        for i, (price, qty) in enumerate(zip(ask_prices, ask_quantities)):
            cumulative_ask_qty += qty
            if cumulative_ask_qty >= 1.0:  # 1 BTC
                price_impact_buy = (price - best_ask) / best_ask * 100
                break
        
        for i, (price, qty) in enumerate(zip(bid_prices, bid_quantities)):
            cumulative_bid_qty += qty
            if cumulative_bid_qty >= 1.0:  # 1 BTC
                price_impact_sell = (best_bid - price) / best_bid * 100
                break
        
        # 深度质量评分
        depth_quality = min(100, (total_volume_20 / 10) * (1 / (spread_pct + 0.001)))
        
        return {
            'timestamp': timestamp,
            'best_bid': best_bid,
            'best_ask': best_ask,
            'spread': spread,
            'spread_pct': spread_pct,
            'bid_volume_5': bid_volume_5,
            'ask_volume_5': ask_volume_5,
            'bid_volume_10': bid_volume_10,
            'ask_volume_10': ask_volume_10,
            'bid_volume_20': bid_volume_20,
            'ask_volume_20': ask_volume_20,
            'imbalance_5': imbalance_5,
            'imbalance_10': imbalance_10,
            'imbalance_20': imbalance_20,
            'liquidity_score': liquidity_score,
            'price_impact_buy_1btc': price_impact_buy,
            'price_impact_sell_1btc': price_impact_sell,
            'depth_quality': depth_quality
        }

    def download_orderbook_snapshots(self, days: int = 30) -> pd.DataFrame:
        """
        下载指定天数的订单簿快照数据
        """
        logger.info(f"开始下载Binance订单簿快照，目标天数: {days}")
        
        orderbook_data = []
        
        # 计算时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        current_time = start_time
        interval_delta = timedelta(minutes=self.snapshot_interval_minutes)
        
        total_snapshots = int((end_time - start_time).total_seconds() / (self.snapshot_interval_minutes * 60))
        snapshot_count = 0
        
        logger.info(f"预计需要获取 {total_snapshots} 个快照，间隔 {self.snapshot_interval_minutes} 分钟")
        
        while current_time <= end_time:
            try:
                # 获取订单簿快照
                orderbook_snapshot = self.get_orderbook_snapshot()
                
                if orderbook_snapshot:
                    # 分析订单簿数据
                    analysis = self.analyze_orderbook(orderbook_snapshot, current_time)
                    
                    if analysis:
                        orderbook_data.append(analysis)
                        snapshot_count += 1
                        
                        if snapshot_count % 24 == 0:  # 每6小时记录一次进度
                            progress = (snapshot_count / total_snapshots) * 100
                            logger.info(f"已获取 {snapshot_count} 个订单簿快照，进度: {progress:.1f}%, 当前时间: {current_time}")
                
                current_time += interval_delta
                time.sleep(1)  # API限制
                
            except Exception as e:
                logger.error(f"下载订单簿快照时出错: {e}")
                current_time += interval_delta
                continue

        if orderbook_data:
            df = pd.DataFrame(orderbook_data)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            logger.info(f"订单簿快照下载完成，共 {len(df)} 条记录")
            logger.info(f"数据时间范围: {df.index.min()} 到 {df.index.max()}")
            return df
        else:
            logger.error("未能下载到任何订单簿数据")
            return pd.DataFrame()

    def save_orderbook_data(self, orderbook_df: pd.DataFrame) -> bool:
        """
        保存订单簿数据为FreqTrade兼容格式
        """
        if orderbook_df.empty:
            logger.error("没有订单簿数据可保存")
            return False
        
        try:
            output_file = self.orderbook_dir / f"{self.symbol}_orderbook.feather"
            orderbook_df.to_feather(output_file)
            
            logger.info(f"订单簿数据保存完成，共 {len(orderbook_df)} 条记录")
            logger.info(f"数据时间范围: {orderbook_df.index.min()} 到 {orderbook_df.index.max()}")
            logger.info(f"数据保存至: {output_file}")
            
            # 保存统计信息
            stats = {
                'total_snapshots': len(orderbook_df),
                'avg_spread_pct': orderbook_df['spread_pct'].mean(),
                'avg_imbalance_10': orderbook_df['imbalance_10'].mean(),
                'avg_liquidity_score': orderbook_df['liquidity_score'].mean(),
                'avg_depth_quality': orderbook_df['depth_quality'].mean(),
                'time_range': f"{orderbook_df.index.min()} 到 {orderbook_df.index.max()}"
            }
            
            logger.info(f"订单簿统计: {stats}")
            return True
            
        except Exception as e:
            logger.error(f"保存订单簿数据时出错: {e}")
            return False

    def download_orderbook_data(self, days: int = 30) -> bool:
        """
        下载订单簿数据的主方法
        """
        logger.info(f"开始下载 {days} 天的订单簿数据...")
        
        # 下载订单簿快照
        orderbook_df = self.download_orderbook_snapshots(days)
        if orderbook_df.empty:
            logger.error("订单簿数据下载失败")
            return False
        
        # 保存订单簿数据
        return self.save_orderbook_data(orderbook_df)

def main():
    """主函数 - 下载30天订单簿数据"""
    downloader = OrderbookDataDownloader("BTCUSDT")
    
    success = downloader.download_orderbook_data(days=30)
    
    if success:
        logger.info("✅ 订单簿数据下载完成！")
    else:
        logger.error("❌ 订单簿数据下载失败")

if __name__ == "__main__":
    main()
